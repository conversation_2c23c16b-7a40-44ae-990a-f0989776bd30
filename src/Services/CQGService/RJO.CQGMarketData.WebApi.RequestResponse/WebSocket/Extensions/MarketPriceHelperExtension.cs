using Metadata2;

namespace RJO.CQGMarketData.WebApi.WebSocket.Extensions;

public static class MarketPriceHelperExtension
{
	public static long GetPriceToMarketScaledPrice(this decimal price, ContractMetadata metadata, double conversionFactor) =>
		Convert.ToInt64(Convert.ToDouble(price) / metadata.CorrectPriceScale / conversionFactor);

	public static double GetPriceFromMarketScaledPrice(this int price, ContractMetadata metadata, double conversionFactor) =>
		Convert.ToDouble(price) * metadata.CorrectPriceScale * conversionFactor;

	public static double GetPriceFromMarketScaledPrice(this long price, ContractMetadata metadata, double conversionFactor) =>
		Convert.ToDouble(price) * metadata.CorrectPriceScale * conversionFactor;

	public static double? GetPriceFromMarketScaledPrice(this double? price,
		double correctPriceScale,
		double conversionFactor) =>
		price * correctPriceScale * conversionFactor;

	public static double GetPriceToMarketScaledPriceDouble(this decimal price, ContractMetadata metadata, double conversionFactor) =>
		Convert.ToDouble(price) / metadata.CorrectPriceScale / conversionFactor;
}
