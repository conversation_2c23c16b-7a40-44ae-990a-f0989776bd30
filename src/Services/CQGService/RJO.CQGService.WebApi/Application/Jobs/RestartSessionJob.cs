using Hangfire;
using Hangfire.Server;
using RJO.CQGService.WebApi.WebSocket;

namespace RJO.CQGService.WebApi.Application.Jobs;

public sealed record RestartSessionJobOptions;

[Queue("default")]
public class RestartSessionJob
{
	readonly CqgHostService _cqgHostService;

	public RestartSessionJob(CqgHostService cqgHostService) => _cqgHostService = cqgHostService;

	public async Task Perform(RestartSessionJobOptions jobOptions, PerformContext performContext) => await _cqgHostService.RestartSessionAsync();
}
