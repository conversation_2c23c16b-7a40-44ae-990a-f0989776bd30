using Order2;
using MediatR;
using RJO.CQGService.WebApi.Application.Commands;
using RJO.CQGService.WebApi.Application.Enumeration;
using RJO.CQGService.Contracts.Market;
using RJO.CQGService.WebApi.WebSocket;
using RJO.CQGService.WebApi.WebSocket.Extensions;
using System.Runtime.Serialization;
using static Order2.Order.Types;
using Decimal = Cqg.Decimal;

namespace RJO.CQGService.WebApi.Application.Handlers;

[DataContract]
public class CreateOfferLimitCommand : CqgCommandBase, IRequest<OfferLimitCreationStatus>
{
	[DataMember]
	public string ClOrderId { get; set; }

	[DataMember]
	public Duration Duration { get; set; }

	[DataMember]
	public decimal Price { get; set; }

	[DataMember]
	public DateTime GoodThruDate { get; set; }

	[DataMember]
	public Decimal Qty { get; set; }

	[DataMember]
	public Side Side { get; set; }

	[DataMember]
	public string Instrument { get; set; }

	[DataMember]
	public string TenantId { get; set; }

	[DataMember]
	public int AccountId { get; set; }

	public string OnBehalfOfUser { get; set; }

	public CreateOfferLimitCommand()
	{
	}

	public CreateOfferLimitCommand(string clOrderId,
		Duration duration,
		decimal price,
		DateTime goodThruDate,
		Decimal quantity,
		Side side,
		string instrument,
		string tenantId,
		int accountId,
		string onBehalfOfUser)
	{
		ClOrderId = clOrderId;
		Duration = duration;
		Price = price;
		GoodThruDate = goodThruDate;
		Qty = quantity;
		Side = side;
		Instrument = instrument;
		TenantId = tenantId;
		AccountId = accountId;
		OnBehalfOfUser = onBehalfOfUser;
	}
}

public class CreateOfferLimitCommandHandler : IRequestHandler<CreateOfferLimitCommand, OfferLimitCreationStatus>
{
	readonly ILogger<CreateOfferLimitCommandHandler> _logger;
	readonly CqgHostService _cqgHostService;

	public CreateOfferLimitCommandHandler(ILogger<CreateOfferLimitCommandHandler> logger, CqgHostService cqgHostService)
	{
		_logger = logger ?? throw new ArgumentNullException(nameof(logger));
		_cqgHostService = cqgHostService;
	}

	public async Task<OfferLimitCreationStatus> Handle(CreateOfferLimitCommand request, CancellationToken cancellationToken)
	{
		_logger.LogInformation($"create limit order: start handling request: {request.SerializeObject()} -- at: {DateTime.UtcNow}");
		OfferLimitCreationStatus offerCreationStatus = new();
		try
		{
			await _cqgHostService.SendCreateOrderRequestAsync(
				request.AccountId,
				request.ClOrderId,
				(uint)request.Duration,
				request.Price,
				default,
				request.GoodThruDate,
				request.Qty,
				(uint)request.Side,
				request.Instrument,
				(uint)Order.Types.OrderType.Lmt,
				request.TenantId,
				request.OnBehalfOfUser
			);
			offerCreationStatus.Code = (int)OrderHandlingTypes.Success;
			offerCreationStatus.Status = Enum.GetName(typeof(OrderHandlingStatusTypes), OrderHandlingStatusTypes.Success);
			_logger.LogInformation($"create order: end handling request: {request.SerializeObject()} -- at: {DateTime.UtcNow}");
		}
		catch (Exception ex)
		{
			offerCreationStatus.Code = (int)OrderHandlingTypes.Error;
			offerCreationStatus.Status = Enum.GetName(typeof(OrderHandlingStatusTypes), OrderHandlingStatusTypes.Error);
			offerCreationStatus.Message = ex.Message;
			_logger.LogError($"create order: error in create request: {request.SerializeObject()} -- at: {DateTime.UtcNow}");
			throw;
		}

		return offerCreationStatus;
	}
}
