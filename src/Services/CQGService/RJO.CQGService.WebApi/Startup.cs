using Hangfire;
using HealthChecks.UI.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication;
using RJO.BuildingBlocks.Common.HealthChecks;
using RJO.BuildingBlocks.WebCommon.Configuration;
using RJO.BuildingBlocks.Common.Extensions;
using RJO.BuildingBlocks.Common.Hangfire;
using RJO.CQGService.Persistence.Database;
using RJO.CQGService.WebApi.Configuration.Extensions;
using RJO.CQGService.WebApi.Core.HealthChecks;
using RJO.BuildingBlocks.WebCommon.Authorization;
using OpenTelemetry.Metrics;

namespace RJO.CQGService.WebApi;

static class Startup
{
	const string SwaggerVersion = "S12.0.0.1";
	const string MyAllowSpecificOrigins = "_demo";

	public static IServiceCollection ConfigureApplication(this IServiceCollection services, IConfiguration configuration, IHostEnvironment hostEnvironment)
	{
		// services.AddAllElasticApm();
		services.AddInfrastructureServices(configuration, hostEnvironment);
		services.ConfigureSettings(configuration);
		services.AddHttpContextAccessor();
		services.AddLaunchDarkly(configuration);
		services.AddCors(o => o.AddPolicy("AllowAll", builder =>
		{
			builder.AllowAnyOrigin()
				.AllowAnyMethod()
				.AllowAnyHeader()
				.WithExposedHeaders("Grpc-Status", "Grpc-Message", "Grpc-Encoding", "Grpc-Accept-Encoding");
		}));
		
		var connectionString = configuration.DatabaseDefaultConnectionString();

		services.AddDbContext<AppDbContext>(options =>
		{
			options.UseSqlServer(connectionString, x =>
			{
				x.MigrationsHistoryTable("EFMigration", "CQG");
			});
		});

		services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

		services
			.AddEventBus(configuration)
			.AddJobs();

		services.AddSwaggerConfiguration(SwaggerVersion);

		services.ConfigureHangfireServices(configuration);

		services.AddAuthentication().AddAzureAD(options =>
		{
			options.Instance = configuration["HangfireAuth:Instance"];
			options.Domain = configuration["HangfireAuth:Domain"];
			options.TenantId = configuration["HangfireAuth:TenantId"];
			options.ClientId = configuration["HangfireAuth:ClientId"];
			options.CallbackPath = configuration["HangfireAuth:CallbackPath"];
		});

		services.AddOpenTelemetry()
			.WithMetrics(builder =>
			{
				builder.AddAspNetCoreInstrumentation()
					.AddHangfireInstrumentation()
					.AddPrometheusExporter();
			});

		services.AddAuthentication(configuration);

		services.AddAuthorization(options =>
		{
			options.AddPolicy("HangfirePolicy", builder =>
			{
				builder
					.AddAuthenticationSchemes("AzureAD")
					.RequireAuthenticatedUser();
			});
		});

		services.AddCors(options =>
		{
			options.AddPolicy(MyAllowSpecificOrigins,
				builder =>
				{
					builder.AllowAnyOrigin()
						.AllowAnyHeader()
						.AllowAnyMethod();
				});
		});
		services.ConfigureCommandHandlers();
		services.AddCustomApplicationInsightsTelemetry(configuration, hostEnvironment);

		services
			.AddHealthChecks()
			.AddApplicationHealthChecks<AppDbContext>(configuration)
			.AddCheck<CqgClientAppCheck>("CqgClient");

		services.AddControllers();
		services.ConfigureHostServices(configuration);

		return services;
	}

	public static IApplicationBuilder ConfigureApplication(this IApplicationBuilder app)
	{
		var env = app.ApplicationServices.GetRequiredService<IHostEnvironment>();

		if (env.IsDevelopment() || env.IsLocal())
		{
			app.UseDeveloperExceptionPage();
		}

		app.UseRouting();
		app.UseAuthentication();
		app.UseAuthorization();
		
		if (env.IsDevelopment() || env.IsLocal())
		{
			app.UseSwagger();
			app.UseSwaggerUI(c =>
			{
				c.SwaggerEndpoint("/swagger/v1/swagger.json", "Catalog.API V1");
				c.RoutePrefix = string.Empty;
			});
		}
		GlobalConfiguration.Configuration.UseActivator(new ContainerJobActivator(app.ApplicationServices));

		app.UseEndpoints(endpoints =>
		{
			endpoints.MapHealthChecks("/health", new() { ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse });
			endpoints.MapControllers();
			endpoints.MapHangfireDashboardWithLock();
				// .RequireAuthorization("HangfirePolicy");
			endpoints.MapObservability();
		});
		app.ConfigureJobs();

		return app;
	}
}
