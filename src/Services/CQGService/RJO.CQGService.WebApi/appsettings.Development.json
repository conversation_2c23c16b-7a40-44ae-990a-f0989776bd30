{"Sentry": {"Environment": "Development"}, "Logging": {"LogLevel": {"RJO.CQGService.WebApi.WebSocket": "Information", "RJO.CQGService.WebApi.Application": "Error", "RJO.CQGService.WebApi.WebSocket.OrderProcessor.OrderProcessorManager": "Error"}}, "Azure": {"AppConfiguration": {"Endpoint": "https://dv-eastus-appconfiguration.azconfig.io"}, "KeyVault": {"Endpoint": "https://dv-eastus-rjohrvyst-kv.vault.azure.net/"}}, "Settings": {"Cqg": {"AllowedSubscriptions": "F.US.ZCE;F.US.ZSE;F.US.ZRE;F.US.KWE", "ConversionFactor": "ZCE:0.01;ZSE:0.01;ZRE:1;KWE:0.01", "HostName": "wss://demoapi.cqg.com:443", "PreloadSpreadSymbols": "F.US.ZCES1K23;F.US.ZCES2H23;F.US.ZCES1K23;F.US.ZCES2K23;F.US.ZSES1F23;F.US.ZSES1H23;F.US.ZSES2H23", "OrderForCurrentUserOnly": true}}, "HangfireAuth": {"ClientId": "90d0a36f-dfed-48ba-aafd-bd5e2f0046e5", "TenantId": "fa6d831d-dc2b-41a9-b180-55610cc31345", "ObjectId": "20db9208-9c2d-4851-8d18-8734cfd95b04", "Instance": "https://login.microsoftonline.com/", "Domain": "https://dv-eastus-cqgservice-app.azurewebsites.net", "CallbackPath": "/signin-oidc"}}