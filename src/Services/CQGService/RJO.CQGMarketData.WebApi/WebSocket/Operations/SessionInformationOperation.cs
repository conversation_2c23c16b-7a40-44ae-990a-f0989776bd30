using WebAPI2;
using TradingSession2;
using Google.Protobuf;
using Microsoft.Extensions.Options;
using RJO.CQGMarketData.WebApi.Configuration.Models;
using RJO.CQGMarketData.WebApi.WebSocket.Extensions;
using RJO.CQGMarketData.WebApi.WebSocket.Session;

namespace RJO.CQGMarketData.WebApi.WebSocket.Operations;

public class SessionInformationOperation
{
	readonly ILogger<SessionInformationOperation> _logger;
	readonly CqgSettings _cqgSettings;
	readonly SessionStateValues _sessionStateValues;

	public SessionInformationOperation(ILogger<SessionInformationOperation> logger,
		IOptions<Settings> settings,
		SessionStateValues sessionStateValues)
	{
		_logger = logger ?? throw new ArgumentNullException(nameof(logger));
		_sessionStateValues = sessionStateValues;
		_cqgSettings = settings.Value.Cqg ?? throw new ArgumentNullException(nameof(settings));
	}

	public IMessage<ClientMsg> CreateRequest(int sessionInfoId, string symbol)
	{
		_logger.LogSessionInformation($"Create request started with session information id {sessionInfoId} for {symbol}");
		if (_sessionStateValues.SessionInformationBySessionInfo.TryGetValue(sessionInfoId, out _))
		{
			_logger.LogSessionInformation($"Session for {symbol} was already requested");
			return null;
		}

		var request = new InformationRequest();
		request.SessionInformationRequest = new();
		request.SessionInformationRequest.FromUtcTime =
			_sessionStateValues.BaseDateTime.GetMillisecondsFromBaseDateTime(DateTime.UtcNow.Date);
		request.SessionInformationRequest.SessionInfoId = sessionInfoId;
		request.SessionInformationRequest.ToUtcTime =
			_sessionStateValues.BaseDateTime.GetMillisecondsFromBaseDateTime(DateTime.UtcNow.Date.AddDays(_cqgSettings.SessionDays));
		var clientMessage = new ClientMsg();
		request.Id = RequestIdExtension.CreateRequestId();
		clientMessage.InformationRequests.Add(request);
		_sessionStateValues.SessionInformationAlreadyRequested.AddOrUpdate(symbol, key => sessionInfoId, (key, value) => sessionInfoId);
		_logger.LogSessionInformation($"Session information request is {clientMessage.SerializeObject()}");
		return clientMessage;
	}

	public IMessage<ClientMsg> CreateRequestWithSubscription(int sessionInfoId, string symbol, bool subscribe = false)
	{
		_logger.LogSessionInformation($"Create request started with session information id {sessionInfoId} for {symbol}");
		if (_sessionStateValues.SessionInformationBySessionInfo.TryGetValue(sessionInfoId, out _))
		{
			_logger.LogSessionInformation($"Session for {symbol} was already requested");
			return null;
		}

		var request = new InformationRequest();
		request.SessionInformationRequest = new();
		request.SessionInformationRequest.FromUtcTime =
			_sessionStateValues.BaseDateTime.GetMillisecondsFromBaseDateTime(DateTime.UtcNow.Date);
		request.SessionInformationRequest.SessionInfoId = sessionInfoId;
		if (!subscribe)
		{
			request.SessionInformationRequest.ToUtcTime =
				_sessionStateValues.BaseDateTime.GetMillisecondsFromBaseDateTime(DateTime.UtcNow.Date.AddDays(5)); // TODO: make it customizable
		}

		var clientMessage = new ClientMsg();
		request.Id = RequestIdExtension.CreateRequestId();
		clientMessage.InformationRequests.Add(request);
		_sessionStateValues.SessionInformationAlreadyRequested.AddOrUpdate(symbol, key => sessionInfoId, (key, value) => sessionInfoId);
		_logger.LogSessionInformation($"Session information request is {clientMessage.SerializeObject()}");
		return clientMessage;
	}

	public void HandleResponse(SessionInformationReport report)
	{
		if (report == null) return;
		_logger.LogSessionInformation($"Session information report received for session info id {report.SessionInfoId} and report {report.SerializeObject()}");
		_sessionStateValues.SessionInformationBySessionInfo.AddOrUpdate(report.SessionInfoId, key => report, (key, value) => report);
	}
}
