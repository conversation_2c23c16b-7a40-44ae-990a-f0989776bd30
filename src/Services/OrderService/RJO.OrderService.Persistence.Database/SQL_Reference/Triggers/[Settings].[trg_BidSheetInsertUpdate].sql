CREATE TRIGGER [Settings].[trg_BidSheetInsertUpdate] 
   ON  [Settings].[BidSheet] 
   AFTER INSERT, UPDATE
AS 
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(6);
    	SET @action = 'Create'; -- Set Action to Insert by default.
    	IF EXISTS(SELECT * FROM DELETED)
    	BEGIN
    		SET @action = 'Update'
    	END

	DECLARE @loginame NVARCHAR(128)
	  SELECT @loginame = loginame
	FROM sys.sysprocesses 
	WHERE spid = @@SPID

	INSERT INTO [Audit].[BidSheetSettings]
	(
		AuditId,
		Id,
		CommodityId,
		DeliveryLocationId,
		CropYear,
		Delivery,
		FutureMonth,
		DeliveryStart,
		DeliveryEnd,
		Basis,
		TenantId,
		IsActive,
		CreatedOn,
		UpdatedOn,
		CreatedBy,
		UpdatedBy,
		AuditAction,
		AuditTime,
		SQLUser,
		ShouldNotify
	)
	SELECT NEWID(),
		Id,
		CommodityId,
		DeliveryLocationId,
		<PERSON><PERSON><PERSON><PERSON>,
		Del<PERSON>y,
		FutureMonth,
		DeliveryStart,
		DeliveryEnd,
		Basis,
		TenantId,
		IsActive,
		CreatedOn,
		UpdatedOn,
		CreatedBy,
		UpdatedBy,
		@action,
		GETDATE(),
		@loginame,
		ShouldNotify
	FROM inserted
END









-- Bisheetstaging
CREATE TRIGGER [Staging].[trg_BidSheetInsertUpdate]
   ON  [Staging].[BidSheet]
   AFTER INSERT,UPDATE
AS 
BEGIN
	SET NOCOUNT ON;

    DECLARE @action as varchar(6);
    	SET @action = 'Create'; -- Set Action to Insert by default.
    	IF EXISTS(SELECT * FROM DELETED)
    	BEGIN
    		SET @action = 'Update'
    	END

	DECLARE @loginame NVARCHAR(128)
	  SELECT @loginame = loginame
	FROM sys.sysprocesses 
	WHERE spid = @@SPID

	INSERT INTO [Audit].[BidSheetStaging]
	(
		AuditId,
		Id,
		FileId,
		CommodityId,
		DeliveryLocationId,
		CropYear,
		Delivery,
		DeliveryStart,
		DeliveryEnd,
		FutureMonth,
		Basis,
		OldFutureMonth,
		OldBasis,
		[Difference],
		Change,
		TenantId,
		IsActive,
		CreatedOn,
		UpdatedOn,
		CreatedBy,
		UpdatedBy,
		AuditAction,
		AuditTime,
		SQLUser,
		ShouldNotify
	)
	SELECT NEWID(),
		Id,
		FileId,
		CommodityId,
		DeliveryLocationId,
		CropYear,
		Delivery,
		DeliveryStart,
		DeliveryEnd,
		FutureMonth,
		Basis,
		OldFutureMonth,
		OldBasis,
		[Difference],
		Change,
		TenantId,
		IsActive,
		CreatedOn,
		UpdatedOn,
		CreatedBy,
		UpdatedBy,
		@action,
		GETDATE(),
		@loginame,
		ShouldNotify
	FROM inserted

END
