exec sp_executesql N'CREATE or alter   procedure [Logs].[SPReportPendingOffers](
            	@tenantid		uniqueidentifier,
            	@limit			int			 = 10,
            	@start			int			 = 1,
            	@commodity		varchar(max) = null,
            	@destination	varchar(max) = null, 
            	@employee		varchar(max) = null,
            	@futureM		varchar(max) = null,
            	@gtc 			varchar(10)  = null,
            	@expiration 	datetime     = null,
            	@userEmail		varchar(max) = null,
                @region			VARCHAR(max) = NULL,
    			@isGroupedPermissionEnabled BIT = 0 
            )
            As
            begin
            	declare @tDestination	table(Id uniqueidentifier);
            	declare @tCommodity		table(Id uniqueidentifier);
            	declare @tEmployee		table(Id uniqueidentifier);
            	declare @tFutureM		table(Id varchar(10));
            	declare @gtcBit			bit;
            	declare @tEmployeeLocations	table(Id uniqueidentifier);
    			DECLARE @tGroupedEmployeeLocations TABLE (
    									Id uniqueidentifier,
    									DestinationId uniqueidentifier,
    									CanBuy bit,
    									CanSell bit,
    									ContractLocationId uniqueidentifier,
    									RegionId uniqueidentifier
    			);
    			declare @tRegion		table(Id uniqueidentifier);

                IF @userEmail is not null
                BEGIN
                    INSERT INTO @tEmployeeLocations
                        SELECT LocationId AS Id FROM Settings.LocationEmployee WHERE EmployeeId = 
                            (SELECT TOP(1) Id FROM Settings.Employee WHERE Email = @userEmail)
                        AND (CanBuy = 1 OR CanSell = 1) AND IsActive = 1
    				INSERT INTO @tGroupedEmployeeLocations
    					SELECT DISTINCT
    						gle.Id,
    						gl.DestinationLocationId AS DestinationId,
    						gle.CanBuy,
    						gle.CanSell,
    						gl.ContractLocationId AS ContractLocationId,
    						gl.RegionId AS RegionId
    					FROM Settings.GroupedLocationEmployee gle
    					INNER JOIN Settings.GroupedLocation gl ON gle.GroupedLocationId = gl.Id
    					WHERE 
    					(gle.CanBuy = 1 OR gle.CanSell = 1) AND gle.IsActive = 1
    					AND EmployeeId = (SELECT TOP(1) Id FROM Settings.Employee WHERE Email = @userEmail)
                END
            	if @destination is not null and len(@destination) > 0
            	begin
            		insert into @tDestination
            		select value from string_split(@destination,'','')
            	end
            	else
            	begin
            		set @destination = null;
            	end

            	if @commodity is not null and len(@commodity) > 0
            	begin
            		insert into @tCommodity
            		select value from string_split(@commodity,'','')
            	end
            	else
            	begin
            		set @commodity = null;
            	end

            	if @employee is not null and len(@employee) > 0
            	begin
            		insert into @tEmployee
            		select value from string_split(@employee,'','')
            	end
            	else
            	begin
            		set @employee = null;
            	end

            	if @futureM is not null and len(@futureM) > 0
            	begin
            		insert into @tFutureM
            		select value from string_split(@futureM,'','')
            	end
            	else
            	begin
            		set @futureM = null;
            	end
            	
            	if @gtc is not null and len(@gtc) > 0
            	begin
            		if @gtc = ''Yes''
            		begin
            			set @gtcBit=1;
            		end
            		else
            		begin
            			set @gtcBit=0;
            		end
            	end
            	else
            	begin
            		set @gtcBit = null;
            	end
            	if @region is not null and len(@region) > 0
    				begin
    					insert into @tRegion
    					select value from string_split(@region,'','')
    				end
    			else
    				begin
    					set @region = null;
    				end
            	declare @count int;
            	select @count = count(o.Id)
            	from Transactions.Offer o
            	where o.TenantId=@tenantid and o.status in (''PartiallyFilled'',''Working'',''Pending'')
            		and (@destination	is null or o.DeliveryLocationId	in (select Id from @tDestination))
            		and (@employee		is null or o.EmployeeId			in (select Id from @tEmployee))
            		and (@commodity		is null or o.CommodityId		in (select Id from @tCommodity))
            		and (@gtcBit		is null or o.Gtc = @gtcBit)
            		and (@expiration	is null or o.Expiration = @expiration)
                    AND (
    					(@isGroupedPermissionEnabled = 0 AND (@userEmail IS NULL OR o.LocationId IN (SELECT Id FROM @tEmployeeLocations)))
    						OR 
    						(
    						@isGroupedPermissionEnabled = 1
    						AND EXISTS (
    							SELECT 1
    							FROM @tGroupedEmployeeLocations gle
    							WHERE 
    							(@userEmail IS NULL OR gle.DestinationId = o.DeliveryLocationId)
    								AND (@userEmail IS NULL OR gle.ContractLocationId = o.LocationId)
    								AND (@userEmail IS NULL OR gle.RegionId = o.RegionId)
    							)
    						)
    				)                
            		and (@futureM		is null or o.FuturesMonth		in (select Id from @tFutureM))
    				AND (@region		IS NULL OR o.RegionId		IN (select Id from @tRegion));
         
            	select
            		@count as RJOCount,
            		o.Id,
            		o.CreatedOn,
            		o.Number,
            		case o.IsSell when 1 then ''S'' else ''P'' end as IsSell,
            		c.Name as ContractType,
            		case o.Gtc when 1 then ''GTC'' else CONVERT(varchar, o.Expiration, 23) end as Expiration,
            		cu.FirstName + '' '' + cu.LastName as Customer,
            		e.Number as ERPNumber,
            		e.FirstName + '' '' + e.LastName as Employee,
            		co.Name as Commodity,
            		o.CropYear,
            		o.FuturesMonth,
            		l.Name as Destination,
            		o.Quantity,
            		isnull(o.FuturesPrice,0) as Futures,
            		isnull(o.NetBasis,0) as Basis,
            		isnull(o.Price,0) as CashPrice,
            		case when o.ContractParentId is null then 0 else 1 end as PricingViaOffer,
            		o.Comments,
            		@count rjoCount,
    				rr.Name as Region
            	from Transactions.Offer o
            	inner join Catalogs.ContractType c on c.Id = o.ContractTypeId
            	inner join Settings.Employee e on e.Id = o.EmployeeId
            	inner join Settings.Customer cu on o.CustomerId = cu.Id
            	inner join Settings.Commodity co on o.CommodityId = co.Id
            	inner join Settings.[Location] l on o.DeliveryLocationId = l.Id
    			LEFT OUTER JOIN Settings.Region rr on rr.Id = o.RegionId
            	where o.TenantId=@tenantid and o.status in (''PartiallyFilled'',''Working'',''Pending'')
            	AND (
    					(@isGroupedPermissionEnabled = 0 AND (@userEmail IS NULL OR o.LocationId IN (SELECT Id FROM @tEmployeeLocations)))
    						OR 
    						(
    						@isGroupedPermissionEnabled = 1
    						AND EXISTS (
    							SELECT 1
    							FROM @tGroupedEmployeeLocations gle
    							WHERE 
    							(@userEmail IS NULL OR gle.DestinationId = o.DeliveryLocationId)
    								AND (@userEmail IS NULL OR gle.ContractLocationId = o.LocationId)
    								AND (@userEmail IS NULL OR gle.RegionId = o.RegionId)
    							)
    						)
    				)			
            		and (@employee		is null or o.EmployeeId			in (select Id from @tEmployee))
            		and (@commodity		is null or o.CommodityId		in (select Id from @tCommodity))
            		and (@gtcBit		is null or o.Gtc = @gtcBit)
            		and (@expiration	is null or o.Expiration = @expiration)
            		and (@futureM		is null or o.FuturesMonth		in (select Id from @tFutureM))
                    and (@destination	is null or o.DeliveryLocationId	in (select Id from @tDestination))
    				AND (@region		IS NULL OR o.RegionId		IN (select Id from @tRegion))
                order by o.UpdatedOn desc
            	OFFSET @limit * (@start - 1) ROWS
                FETCH NEXT @limit ROWS ONLY OPTION (RECOMPILE);
        end'