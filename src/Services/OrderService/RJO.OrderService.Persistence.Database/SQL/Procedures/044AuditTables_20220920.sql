-- delete triggers
if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_ContractInsertUpdate')
	exec sp_executesql N'drop trigger [Transactions].[trg_ContractInsertUpdate]'
go

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_ContractDelete')
	exec sp_executesql N'drop trigger [Transactions].[trg_ContractDelete]'
go
--
if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_OfferInsertUpdate')
	exec sp_executesql N'drop trigger [Transactions].[trg_OfferInsertUpdate]'
go

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_OfferDelete')
	exec sp_executesql N'drop trigger [Transactions].[trg_OfferDelete]'
go
--
if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_MarketTransactionInsertUpdate')
	exec sp_executesql N'drop trigger [Transactions].[trg_MarketTransactionInsertUpdate]'
go

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_MarketTransactionDelete')
	exec sp_executesql N'drop trigger [Transactions].[trg_MarketTransactionDelete]'
go
--
if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_ContractMetadataInsertUpdate')
	exec sp_executesql N'drop trigger [Transactions].[trg_ContractMetadataInsertUpdate]'
go

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_ContractMetadataDelete')
	exec sp_executesql N'drop trigger [Transactions].[trg_ContractMetadataDelete]'
go
--
if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_OfferMetadataInsertUpdate')
	exec sp_executesql N'drop trigger [Transactions].[trg_OfferMetadataInsertUpdate]'
go

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Transactions' and  o.name = 'trg_OfferMetadataDelete')
	exec sp_executesql N'drop trigger [Transactions].[trg_OfferMetadataDelete]'
go

-- delete audit tables

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Audit' and  o.name='Contract')
	exec sp_executesql N'drop table [Audit].[Contract]'
go

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Audit' and  o.name='Offer')
	exec sp_executesql N'drop table [Audit].[Offer]'
go

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Audit' and  o.name='MarketTransaction')
	exec sp_executesql N'drop table [Audit].[MarketTransaction]'
go

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Audit' and  o.name='ContractMetadata')
	exec sp_executesql N'drop table [Audit].[ContractMetadata]'
go

if exists (select * from sys.objects o, sys.schemas s where o.schema_id=s.schema_id and s.name='Audit' and  o.name='OfferMetadata')
	exec sp_executesql N'drop table [Audit].[OfferMetadata]'
go

if exists (select * from sys.schemas where  name='Audit')
	exec sp_executesql N'drop schema [Audit]'
go

-- Create schema
exec sp_executesql N'create schema [Audit]'
go


-- create audit tables
exec sp_executesql N'
CREATE TABLE [Audit].[Contract](
	[AuditId] [uniqueidentifier],
	[Id] [uniqueidentifier] NOT NULL,
	[Status] [nvarchar](8) NULL,
	[Event] [nvarchar](max) NULL,
	[TransactionTypeId] [uniqueidentifier] NOT NULL,
	[ContractTypeId] [uniqueidentifier] NOT NULL,
	[IsDeliveryDatesCustom] [bit] NOT NULL,
	[IsSell] [bit] NOT NULL,
	[CommodityId] [uniqueidentifier] NOT NULL,
	[LocationId] [uniqueidentifier] NOT NULL,
	[DeliveryLocationId] [uniqueidentifier] NOT NULL,
	[DeliveryStartDate] [datetime2](7) NOT NULL,
	[DeliveryEndDate] [datetime2](7) NOT NULL,
	[CropYear] [smallint] NOT NULL,
	[CustomerId] [uniqueidentifier] NOT NULL,
	[EmployeeId] [uniqueidentifier] NOT NULL,
	[UpdatedEmployeeId] [uniqueidentifier] NOT NULL,
	[FuturesMonth] [nvarchar](24) NULL,
	[FuturesPrice] [decimal](18, 4) NULL,
	[PostedBasis] [decimal](18, 4) NULL,
	[PushBasis] [decimal](18, 4) NULL,
	[NetBasis] [decimal](18, 4) NULL,
	[FreightPrice] [decimal](18, 4) NOT NULL,
	[Fees1] [decimal](18, 4) NOT NULL,
	[Fees2] [decimal](18, 4) NOT NULL,
	[Price] [decimal](18, 4) NULL,
	[Quantity] [decimal](18, 2) NOT NULL,
	[Comments] [nvarchar](max) NULL,
	[Number] [nvarchar](50) NULL,
	[PassFill] [bit] NOT NULL,
	[DoNotHedge] [bit] NOT NULL,
	[CashSettlement] [bit] NOT NULL,
	[ChildCount] [int] NOT NULL,
	[ChildOffersCount] [int] NOT NULL,
	[RootParentId] [uniqueidentifier] NULL,
	[ParentId] [uniqueidentifier] NULL,
	[RemainingBalance] [decimal](18, 2) NOT NULL,
	[OfferId] [uniqueidentifier] NULL,
	[TenantId] [uniqueidentifier] NOT NULL,
	[IsActive] [bit] NOT NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[UpdatedOn] [datetime2](7) NULL,
	[CreatedBy] [nvarchar](50) NOT NULL,
	[UpdatedBy] [nvarchar](50) NULL,
	[InternalCode] [nvarchar](22) NULL,
	[Expiration] [datetime2](7) NULL,
	[ErpMessage] [nvarchar](max) NULL,
	[ErpStatus] [int] NOT NULL,
	[Source] [nvarchar](24) NULL,
	[SeqId] [bigint] NOT NULL,
	[LastTransactionQuantity] [decimal](18, 2) NOT NULL,
	[GrossRemainingBalance] [decimal](18, 2) NOT NULL,
	[RealCropYear] [smallint] NOT NULL,
	[TheirContract] [nvarchar](25) NULL,
	[AuditAction] nvarchar(20),
    [AuditTime] datetime
) 
'
go

exec sp_executesql N'
CREATE TABLE [Audit].Offer(
	[AuditId] [uniqueidentifier],
    [Id] [uniqueidentifier] NOT NULL,
	[Event] [nvarchar](20) NOT NULL,
	[Status] [nvarchar](30) NULL,
	[InternalStatus] [nvarchar](24) NULL,
	[TransactionTypeId] [uniqueidentifier] NOT NULL,
	[ContractTypeId] [uniqueidentifier] NOT NULL,
	[IsSell] [bit] NOT NULL,
	[IsDeliveryDatesCustom] [bit] NOT NULL,
	[CommodityId] [uniqueidentifier] NOT NULL,
	[LocationId] [uniqueidentifier] NOT NULL,
	[DeliveryLocationId] [uniqueidentifier] NOT NULL,
	[DeliveryStartDate] [datetime2](7) NOT NULL,
	[DeliveryEndDate] [datetime2](7) NOT NULL,
	[CropYear] [smallint] NOT NULL,
	[CustomerId] [uniqueidentifier] NOT NULL,
	[EmployeeId] [uniqueidentifier] NOT NULL,
	[UpdatedEmployeeId] [uniqueidentifier] NOT NULL,
	[FuturesMonth] [nvarchar](24) NULL,
	[FuturesPrice] [decimal](18, 4) NULL,
	[PostedBasis] [decimal](18, 4) NULL,
	[PushBasis] [decimal](18, 4) NULL,
	[NetBasis] [decimal](18, 4) NULL,
	[FreightPrice] [decimal](18, 4) NOT NULL,
	[Fees1] [decimal](18, 4) NOT NULL,
	[Fees2] [decimal](18, 4) NOT NULL,
	[Price] [decimal](18, 4) NOT NULL,
	[Quantity] [decimal](18, 2) NOT NULL,
	[Comments] [nvarchar](max) NULL,
	[Number] [nvarchar](30) NULL,
	[Gtc] [bit] NOT NULL,
	[Expiration] [datetime2](7) NULL,
	[CashSettlement] [bit] NOT NULL,
	[IsInternal] [bit] NOT NULL,
	[RemainingBalance] [decimal](18, 2) NOT NULL,
	[ChangeRemainingBalanceOnProcess] [datetime2](7) NOT NULL,
	[RemainingBalanceOnProcess] [decimal](18, 2) NOT NULL,
	[ContractParentId] [uniqueidentifier] NULL,
	[IsOrphan] [bit] NOT NULL,
	[TenantId] [uniqueidentifier] NOT NULL,
	[IsActive] [bit] NOT NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[UpdatedOn] [datetime2](7) NULL,
	[CreatedBy] [nvarchar](50) NOT NULL,
	[UpdatedBy] [nvarchar](50) NULL,
	[HasRejection] [bit] NOT NULL,
	[InternalCode] [nvarchar](22) NULL,
	[Instrument] [nvarchar](20) NULL,
	[FillCount] [smallint] NOT NULL,
	[RealCropYear] [smallint] NOT NULL,
	[TheirContract] [nvarchar](25) NULL,
	[AuditAction] nvarchar(20),
    [AuditTime] datetime
) 
'
go

exec sp_executesql N'
CREATE TABLE [Audit].[MarketTransaction] (
	[AuditId] [uniqueidentifier],
	[Id] [uniqueidentifier] NOT NULL,
	[State] [nvarchar](15) NULL,
	[Type] [nvarchar](6) NOT NULL,
	[Event] [nvarchar](11) NOT NULL,
	[Source] [nvarchar](18) NOT NULL,
	[MarketAccount] [int] NOT NULL,
	[MarketId] [nvarchar](500) NULL,
	[MarketInformation] [nvarchar](max) NULL,
	[MarketPrice] [decimal](18, 4) NOT NULL,
	[ContractPrice] [decimal](18, 4) NOT NULL,
	[FuturesPrice] [decimal](18, 4) NOT NULL,
	[IsSell] [bit] NOT NULL,
	[Quantity] [decimal](18, 2) NOT NULL,
	[Lots] [int] NOT NULL,
	[WorkingLots] [int] NOT NULL,
	[ThresholdUsed] [int] NOT NULL,
	[CommodityId] [uniqueidentifier] NOT NULL,
	[CommodityName] [nvarchar](max) NOT NULL,
	[Instrument] [nvarchar](max) NOT NULL,
	[ContractId] [uniqueidentifier] NULL,
	[OfferId] [uniqueidentifier] NULL,
	[FuturesMonth] [nvarchar](24) NOT NULL,
	[ClientNumber] [nvarchar](30) NOT NULL,
	[InternalNumber] [nvarchar](30) NOT NULL,
	[IsApproved] [bit] NOT NULL,
	[ApprovedDate] [datetime2](7) NULL,
	[Expiration] [datetime2](7) NULL,
	[IsGtc] [bit] NOT NULL,
	[CropYear] [smallint] NOT NULL,
	[WasAcknowledge] [bit] NOT NULL,
	[OldFuturesPrice] [decimal](18, 2) NOT NULL,
	[OldQuantity] [decimal](18, 2) NOT NULL,
	[OldLots] [int] NOT NULL,
	[TenantId] [uniqueidentifier] NOT NULL,
	[IsActive] [bit] NOT NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[UpdatedOn] [datetime2](7) NULL,
	[CreatedBy] [nvarchar](50) NOT NULL,
	[UpdatedBy] [nvarchar](50) NULL,
	[InternalCode] [nvarchar](22) NULL,
	[WasAlreadySent] [bit] NOT NULL,
	[PassFill] [bit] NOT NULL,
	[Comments] [nvarchar](max) NULL,
	[RealCropYear] [smallint] NOT NULL,
	[AuditAction] nvarchar(20),
    [AuditTime] datetime
)
'
go

exec sp_executesql N'
CREATE TABLE [Audit].[ContractMetadata] (
	[AuditId] [uniqueidentifier],
	[Id] [uniqueidentifier] NOT NULL,
	[FieldId] [uniqueidentifier] NULL,
	[ContractId] [uniqueidentifier] NOT NULL,
	[Type] [nvarchar](8) NOT NULL,
	[Value] [nvarchar](255) NULL,
	[TenantId] [uniqueidentifier] NOT NULL,
	[IsActive] [bit] NOT NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[UpdatedOn] [datetime2](7) NULL,
	[CreatedBy] [nvarchar](50) NOT NULL,
	[UpdatedBy] [nvarchar](50) NULL,
	[AuditAction] nvarchar(20),
    [AuditTime] datetime
)
'
go

exec sp_executesql N'
CREATE TABLE [Audit].[OfferMetadata] (
	[AuditId] [uniqueidentifier],
	[Id] [uniqueidentifier] NOT NULL,
	[FieldId] [uniqueidentifier] NULL,
	[OfferId] [uniqueidentifier] NOT NULL,
	[Type] [nvarchar](8) NOT NULL,
	[Value] [nvarchar](255) NULL,
	[TenantId] [uniqueidentifier] NOT NULL,
	[IsActive] [bit] NOT NULL,
	[CreatedOn] [datetime2](7) NOT NULL,
	[UpdatedOn] [datetime2](7) NULL,
	[CreatedBy] [nvarchar](50) NOT NULL,
	[UpdatedBy] [nvarchar](50) NULL,
	[AuditAction] nvarchar(20),
    [AuditTime] datetime
) 
'
go


-- create triggers
exec sp_executesql N'
Create TRIGGER [Transactions].[trg_ContractInsertUpdate] ON [Transactions].[Contract]
	AFTER INSERT, UPDATE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Create''; -- Set Action to Insert by default.
	IF EXISTS(SELECT * FROM DELETED)
	BEGIN
		SET @action = ''Update''
	END

	INSERT INTO [Audit].[Contract]
			   (AuditId, Id
			   ,[Status]
			   ,[Event]
			   ,[TransactionTypeId]
			   ,[ContractTypeId]
			   ,[IsDeliveryDatesCustom]
			   ,[IsSell]
			   ,[CommodityId]
			   ,[LocationId]
			   ,[DeliveryLocationId]
			   ,[DeliveryStartDate]
			   ,[DeliveryEndDate]
			   ,[CropYear]
			   ,[CustomerId]
			   ,[EmployeeId]
			   ,[UpdatedEmployeeId]
			   ,[FuturesMonth]
			   ,[FuturesPrice]
			   ,[PostedBasis]
			   ,[PushBasis]
			   ,[NetBasis]
			   ,[FreightPrice]
			   ,[Fees1]
			   ,[Fees2]
			   ,[Price]
			   ,[Quantity]
			   ,[Comments]
			   ,[Number]
			   ,[PassFill]
			   ,[DoNotHedge]
			   ,[CashSettlement]
			   ,[ChildCount]
			   ,[ChildOffersCount]
			   ,[RootParentId]
			   ,[ParentId]
			   ,[RemainingBalance]
			   ,[OfferId]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[InternalCode]
			   ,[Expiration]
			   ,[ErpMessage]
			   ,[ErpStatus]
			   ,[Source]
			   ,[SeqId]
			   ,[LastTransactionQuantity]
			   ,[GrossRemainingBalance]
			   ,[RealCropYear]
			   ,[TheirContract]
			   ,AuditAction, AuditTime   )
	   SELECT  NEWID(), [Id]
			   ,[Status]
			   ,[Event]
			   ,[TransactionTypeId]
			   ,[ContractTypeId]
			   ,[IsDeliveryDatesCustom]
			   ,[IsSell]
			   ,[CommodityId]
			   ,[LocationId]
			   ,[DeliveryLocationId]
			   ,[DeliveryStartDate]
			   ,[DeliveryEndDate]
			   ,[CropYear]
			   ,[CustomerId]
			   ,[EmployeeId]
			   ,[UpdatedEmployeeId]
			   ,[FuturesMonth]
			   ,[FuturesPrice]
			   ,[PostedBasis]
			   ,[PushBasis]
			   ,[NetBasis]
			   ,[FreightPrice]
			   ,[Fees1]
			   ,[Fees2]
			   ,[Price]
			   ,[Quantity]
			   ,[Comments]
			   ,[Number]
			   ,[PassFill]
			   ,[DoNotHedge]
			   ,[CashSettlement]
			   ,[ChildCount]
			   ,[ChildOffersCount]
			   ,[RootParentId]
			   ,[ParentId]
			   ,[RemainingBalance]
			   ,[OfferId]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[InternalCode]
			   ,[Expiration]
			   ,[ErpMessage]
			   ,[ErpStatus]
			   ,[Source]
			   ,[SeqId]
			   ,[LastTransactionQuantity]
			   ,[GrossRemainingBalance]
			   ,[RealCropYear]
			   ,[TheirContract]
			   ,@action, getdate()
	FROM inserted
End
'
go

exec sp_executesql N'
Create TRIGGER [Transactions].[trg_ContractDelete] ON [Transactions].[Contract]
	AFTER DELETE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Delete''; -- Set Action to Insert by default.

	INSERT INTO [Audit].[Contract]
			   (AuditId, Id
			   ,[Status]
			   ,[Event]
			   ,[TransactionTypeId]
			   ,[ContractTypeId]
			   ,[IsDeliveryDatesCustom]
			   ,[IsSell]
			   ,[CommodityId]
			   ,[LocationId]
			   ,[DeliveryLocationId]
			   ,[DeliveryStartDate]
			   ,[DeliveryEndDate]
			   ,[CropYear]
			   ,[CustomerId]
			   ,[EmployeeId]
			   ,[UpdatedEmployeeId]
			   ,[FuturesMonth]
			   ,[FuturesPrice]
			   ,[PostedBasis]
			   ,[PushBasis]
			   ,[NetBasis]
			   ,[FreightPrice]
			   ,[Fees1]
			   ,[Fees2]
			   ,[Price]
			   ,[Quantity]
			   ,[Comments]
			   ,[Number]
			   ,[PassFill]
			   ,[DoNotHedge]
			   ,[CashSettlement]
			   ,[ChildCount]
			   ,[ChildOffersCount]
			   ,[RootParentId]
			   ,[ParentId]
			   ,[RemainingBalance]
			   ,[OfferId]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[InternalCode]
			   ,[Expiration]
			   ,[ErpMessage]
			   ,[ErpStatus]
			   ,[Source]
			   ,[SeqId]
			   ,[LastTransactionQuantity]
			   ,[GrossRemainingBalance]
			   ,[RealCropYear]
			   ,[TheirContract]
			   ,AuditAction, AuditTime   )
	   SELECT  NEWID(), [Id]
			   ,[Status]
			   ,[Event]
			   ,[TransactionTypeId]
			   ,[ContractTypeId]
			   ,[IsDeliveryDatesCustom]
			   ,[IsSell]
			   ,[CommodityId]
			   ,[LocationId]
			   ,[DeliveryLocationId]
			   ,[DeliveryStartDate]
			   ,[DeliveryEndDate]
			   ,[CropYear]
			   ,[CustomerId]
			   ,[EmployeeId]
			   ,[UpdatedEmployeeId]
			   ,[FuturesMonth]
			   ,[FuturesPrice]
			   ,[PostedBasis]
			   ,[PushBasis]
			   ,[NetBasis]
			   ,[FreightPrice]
			   ,[Fees1]
			   ,[Fees2]
			   ,[Price]
			   ,[Quantity]
			   ,[Comments]
			   ,[Number]
			   ,[PassFill]
			   ,[DoNotHedge]
			   ,[CashSettlement]
			   ,[ChildCount]
			   ,[ChildOffersCount]
			   ,[RootParentId]
			   ,[ParentId]
			   ,[RemainingBalance]
			   ,[OfferId]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[InternalCode]
			   ,[Expiration]
			   ,[ErpMessage]
			   ,[ErpStatus]
			   ,[Source]
			   ,[SeqId]
			   ,[LastTransactionQuantity]
			   ,[GrossRemainingBalance]
			   ,[RealCropYear]
			   ,[TheirContract]
			   ,@action, getdate()
	FROM deleted
End
'
go

exec sp_executesql N'
Create TRIGGER [Transactions].[trg_OfferInsertUpdate] ON [Transactions].Offer
	AFTER INSERT, UPDATE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Create''; -- Set Action to Insert by default.
	IF EXISTS(SELECT * FROM DELETED)
	BEGIN
		SET @action = ''Update''
	END

	INSERT INTO [Audit].[Offer]
			   (AuditId, Id
			   ,[Event]
			   ,[Status]
			   ,[InternalStatus]
			   ,[TransactionTypeId]
			   ,[ContractTypeId]
			   ,[IsSell]
			   ,[IsDeliveryDatesCustom]
			   ,[CommodityId]
			   ,[LocationId]
			   ,[DeliveryLocationId]
			   ,[DeliveryStartDate]
			   ,[DeliveryEndDate]
			   ,[CropYear]
			   ,[CustomerId]
			   ,[EmployeeId]
			   ,[UpdatedEmployeeId]
			   ,[FuturesMonth]
			   ,[FuturesPrice]
			   ,[PostedBasis]
			   ,[PushBasis]
			   ,[NetBasis]
			   ,[FreightPrice]
			   ,[Fees1]
			   ,[Fees2]
			   ,[Price]
			   ,[Quantity]
			   ,[Comments]
			   ,[Number]
			   ,[Gtc]
			   ,[Expiration]
			   ,[CashSettlement]
			   ,[IsInternal]
			   ,[RemainingBalance]
			   ,[ChangeRemainingBalanceOnProcess]
			   ,[RemainingBalanceOnProcess]
			   ,[ContractParentId]
			   ,[IsOrphan]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[HasRejection]
			   ,[InternalCode]
			   ,[Instrument]
			   ,[FillCount]
			   ,[RealCropYear]
			   ,[TheirContract]
			   ,AuditAction, AuditTime   )
	   SELECT  NEWID(), [Id]
			   ,[Event]
			   ,[Status]
			   ,[InternalStatus]
			   ,[TransactionTypeId]
			   ,[ContractTypeId]
			   ,[IsSell]
			   ,[IsDeliveryDatesCustom]
			   ,[CommodityId]
			   ,[LocationId]
			   ,[DeliveryLocationId]
			   ,[DeliveryStartDate]
			   ,[DeliveryEndDate]
			   ,[CropYear]
			   ,[CustomerId]
			   ,[EmployeeId]
			   ,[UpdatedEmployeeId]
			   ,[FuturesMonth]
			   ,[FuturesPrice]
			   ,[PostedBasis]
			   ,[PushBasis]
			   ,[NetBasis]
			   ,[FreightPrice]
			   ,[Fees1]
			   ,[Fees2]
			   ,[Price]
			   ,[Quantity]
			   ,[Comments]
			   ,[Number]
			   ,[Gtc]
			   ,[Expiration]
			   ,[CashSettlement]
			   ,[IsInternal]
			   ,[RemainingBalance]
			   ,[ChangeRemainingBalanceOnProcess]
			   ,[RemainingBalanceOnProcess]
			   ,[ContractParentId]
			   ,[IsOrphan]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[HasRejection]
			   ,[InternalCode]
			   ,[Instrument]
			   ,[FillCount]
			   ,[RealCropYear]
			   ,[TheirContract]
			   ,@action, getdate()
	FROM inserted
End
'
go

exec sp_executesql N'
Create TRIGGER [Transactions].[trg_OfferDelete] ON [Transactions].Offer
	AFTER DELETE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Delete''; -- Set Action to Insert by default.

	INSERT INTO [Audit].[Offer]
			   (AuditId, Id
			   ,[Event]
			   ,[Status]
			   ,[InternalStatus]
			   ,[TransactionTypeId]
			   ,[ContractTypeId]
			   ,[IsSell]
			   ,[IsDeliveryDatesCustom]
			   ,[CommodityId]
			   ,[LocationId]
			   ,[DeliveryLocationId]
			   ,[DeliveryStartDate]
			   ,[DeliveryEndDate]
			   ,[CropYear]
			   ,[CustomerId]
			   ,[EmployeeId]
			   ,[UpdatedEmployeeId]
			   ,[FuturesMonth]
			   ,[FuturesPrice]
			   ,[PostedBasis]
			   ,[PushBasis]
			   ,[NetBasis]
			   ,[FreightPrice]
			   ,[Fees1]
			   ,[Fees2]
			   ,[Price]
			   ,[Quantity]
			   ,[Comments]
			   ,[Number]
			   ,[Gtc]
			   ,[Expiration]
			   ,[CashSettlement]
			   ,[IsInternal]
			   ,[RemainingBalance]
			   ,[ChangeRemainingBalanceOnProcess]
			   ,[RemainingBalanceOnProcess]
			   ,[ContractParentId]
			   ,[IsOrphan]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[HasRejection]
			   ,[InternalCode]
			   ,[Instrument]
			   ,[FillCount]
			   ,[RealCropYear]
			   ,[TheirContract]
			   ,AuditAction, AuditTime   )
	   SELECT  NEWID(), [Id]
			   ,[Event]
			   ,[Status]
			   ,[InternalStatus]
			   ,[TransactionTypeId]
			   ,[ContractTypeId]
			   ,[IsSell]
			   ,[IsDeliveryDatesCustom]
			   ,[CommodityId]
			   ,[LocationId]
			   ,[DeliveryLocationId]
			   ,[DeliveryStartDate]
			   ,[DeliveryEndDate]
			   ,[CropYear]
			   ,[CustomerId]
			   ,[EmployeeId]
			   ,[UpdatedEmployeeId]
			   ,[FuturesMonth]
			   ,[FuturesPrice]
			   ,[PostedBasis]
			   ,[PushBasis]
			   ,[NetBasis]
			   ,[FreightPrice]
			   ,[Fees1]
			   ,[Fees2]
			   ,[Price]
			   ,[Quantity]
			   ,[Comments]
			   ,[Number]
			   ,[Gtc]
			   ,[Expiration]
			   ,[CashSettlement]
			   ,[IsInternal]
			   ,[RemainingBalance]
			   ,[ChangeRemainingBalanceOnProcess]
			   ,[RemainingBalanceOnProcess]
			   ,[ContractParentId]
			   ,[IsOrphan]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[HasRejection]
			   ,[InternalCode]
			   ,[Instrument]
			   ,[FillCount]
			   ,[RealCropYear]
			   ,[TheirContract]
			   ,@action, getdate()
	FROM deleted
End
'
go

exec sp_executesql N'
Create TRIGGER [Transactions].[trg_MarketTransactionInsertUpdate] ON [Transactions].MarketTransaction
	AFTER INSERT, UPDATE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Create''; -- Set Action to Insert by default.
	IF EXISTS(SELECT * FROM DELETED)
	BEGIN
		SET @action = ''Update''
	END

	INSERT INTO [Audit].[MarketTransaction]
			   (AuditId, Id
			   ,[State]
			   ,[Type]
			   ,[Event]
			   ,[Source]
			   ,[MarketAccount]
			   ,[MarketId]
			   ,[MarketInformation]
			   ,[MarketPrice]
			   ,[ContractPrice]
			   ,[FuturesPrice]
			   ,[IsSell]
			   ,[Quantity]
			   ,[Lots]
			   ,[WorkingLots]
			   ,[ThresholdUsed]
			   ,[CommodityId]
			   ,[CommodityName]
			   ,[Instrument]
			   ,[ContractId]
			   ,[OfferId]
			   ,[FuturesMonth]
			   ,[ClientNumber]
			   ,[InternalNumber]
			   ,[IsApproved]
			   ,[ApprovedDate]
			   ,[Expiration]
			   ,[IsGtc]
			   ,[CropYear]
			   ,[WasAcknowledge]
			   ,[OldFuturesPrice]
			   ,[OldQuantity]
			   ,[OldLots]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[InternalCode]
			   ,[WasAlreadySent]
			   ,[PassFill]
			   ,[Comments]
			   ,[RealCropYear]
			   ,AuditAction, AuditTime   )
	   SELECT  NEWID(), [Id]
			   ,[State]
			   ,[Type]
			   ,[Event]
			   ,[Source]
			   ,[MarketAccount]
			   ,[MarketId]
			   ,[MarketInformation]
			   ,[MarketPrice]
			   ,[ContractPrice]
			   ,[FuturesPrice]
			   ,[IsSell]
			   ,[Quantity]
			   ,[Lots]
			   ,[WorkingLots]
			   ,[ThresholdUsed]
			   ,[CommodityId]
			   ,[CommodityName]
			   ,[Instrument]
			   ,[ContractId]
			   ,[OfferId]
			   ,[FuturesMonth]
			   ,[ClientNumber]
			   ,[InternalNumber]
			   ,[IsApproved]
			   ,[ApprovedDate]
			   ,[Expiration]
			   ,[IsGtc]
			   ,[CropYear]
			   ,[WasAcknowledge]
			   ,[OldFuturesPrice]
			   ,[OldQuantity]
			   ,[OldLots]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[InternalCode]
			   ,[WasAlreadySent]
			   ,[PassFill]
			   ,[Comments]
			   ,[RealCropYear]
			   ,@action, getdate()
	FROM inserted
End
'
go

exec sp_executesql N'
Create TRIGGER [Transactions].[trg_MarketTransactionDelete] ON [Transactions].MarketTransaction
	AFTER DELETE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Delete''; -- Set Action to Insert by default.

	INSERT INTO [Audit].[MarketTransaction]
			   (AuditId, Id
			   ,[State]
			   ,[Type]
			   ,[Event]
			   ,[Source]
			   ,[MarketAccount]
			   ,[MarketId]
			   ,[MarketInformation]
			   ,[MarketPrice]
			   ,[ContractPrice]
			   ,[FuturesPrice]
			   ,[IsSell]
			   ,[Quantity]
			   ,[Lots]
			   ,[WorkingLots]
			   ,[ThresholdUsed]
			   ,[CommodityId]
			   ,[CommodityName]
			   ,[Instrument]
			   ,[ContractId]
			   ,[OfferId]
			   ,[FuturesMonth]
			   ,[ClientNumber]
			   ,[InternalNumber]
			   ,[IsApproved]
			   ,[ApprovedDate]
			   ,[Expiration]
			   ,[IsGtc]
			   ,[CropYear]
			   ,[WasAcknowledge]
			   ,[OldFuturesPrice]
			   ,[OldQuantity]
			   ,[OldLots]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[InternalCode]
			   ,[WasAlreadySent]
			   ,[PassFill]
			   ,[Comments]
			   ,[RealCropYear]
			   ,AuditAction, AuditTime   )
	   SELECT  NEWID(), [Id]
			   ,[State]
			   ,[Type]
			   ,[Event]
			   ,[Source]
			   ,[MarketAccount]
			   ,[MarketId]
			   ,[MarketInformation]
			   ,[MarketPrice]
			   ,[ContractPrice]
			   ,[FuturesPrice]
			   ,[IsSell]
			   ,[Quantity]
			   ,[Lots]
			   ,[WorkingLots]
			   ,[ThresholdUsed]
			   ,[CommodityId]
			   ,[CommodityName]
			   ,[Instrument]
			   ,[ContractId]
			   ,[OfferId]
			   ,[FuturesMonth]
			   ,[ClientNumber]
			   ,[InternalNumber]
			   ,[IsApproved]
			   ,[ApprovedDate]
			   ,[Expiration]
			   ,[IsGtc]
			   ,[CropYear]
			   ,[WasAcknowledge]
			   ,[OldFuturesPrice]
			   ,[OldQuantity]
			   ,[OldLots]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,[InternalCode]
			   ,[WasAlreadySent]
			   ,[PassFill]
			   ,[Comments]
			   ,[RealCropYear]
			   ,@action, getdate()
	FROM deleted
End
'
go

exec sp_executesql N'
Create TRIGGER [Transactions].[trg_ContractMetadataInsertUpdate] ON [Transactions].[ContractMetadata]
	AFTER INSERT, UPDATE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Create''; -- Set Action to Insert by default.
	IF EXISTS(SELECT * FROM DELETED)
	BEGIN
		SET @action = ''Update''
	END

	INSERT INTO [Audit].[ContractMetadata]
			   (AuditId, Id
			   ,[FieldId]
			   ,[ContractId]
			   ,[Type]
			   ,[Value]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,AuditAction, AuditTime   )
	   SELECT  NEWID(), [Id]
			   ,[FieldId]
			   ,[ContractId]
			   ,[Type]
			   ,[Value]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,@action, getdate()
	FROM inserted
End
'
go

exec sp_executesql N'
Create TRIGGER [Transactions].[trg_ContractMetadataDelete] ON [Transactions].ContractMetadata
	AFTER DELETE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Delete''; 

	INSERT INTO [Audit].[ContractMetadata]
			   (AuditId, Id
			   ,[FieldId]
			   ,[ContractId]
			   ,[Type]
			   ,[Value]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,AuditAction, AuditTime   )
	   SELECT  NEWID(), [Id]
			   ,[FieldId]
			   ,[ContractId]
			   ,[Type]
			   ,[Value]
			   ,[TenantId]
			   ,[IsActive]
			   ,[CreatedOn]
			   ,[UpdatedOn]
			   ,[CreatedBy]
			   ,[UpdatedBy]
			   ,@action, getdate()
	FROM deleted
End
'
go

exec sp_executesql N'
Create TRIGGER [Transactions].[trg_OfferMetadataInsertUpdate] ON [Transactions].OfferMetadata
	AFTER INSERT, UPDATE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Create''; -- Set Action to Insert by default.
	IF EXISTS(SELECT * FROM DELETED)
	BEGIN
		SET @action = ''Update''
	END

	INSERT INTO [Audit].OfferMetadata
			(AuditId, Id
			,[FieldId]
			,[OfferId]
			,[Type]
			,[Value]
			,[TenantId]
			,[IsActive]
			,[CreatedOn]
			,[UpdatedOn]
			,[CreatedBy]
			,[UpdatedBy]
			,AuditAction, AuditTime   )
	SELECT  NEWID(), [Id]
           ,[FieldId]
           ,[OfferId]
           ,[Type]
           ,[Value]
           ,[TenantId]
           ,[IsActive]
           ,[CreatedOn]
           ,[UpdatedOn]
           ,[CreatedBy]
           ,[UpdatedBy]
			,@action, getdate()
	FROM inserted
End
'
go

exec sp_executesql N'
Create TRIGGER [Transactions].[trg_OfferMetadataDelete] ON [Transactions].OfferMetadata
	AFTER DELETE
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @action as varchar(20);
	SET @action = ''Delete''; 

	INSERT INTO [Audit].OfferMetadata
			(AuditId, Id
			,[FieldId]
			,[OfferId]
			,[Type]
			,[Value]
			,[TenantId]
			,[IsActive]
			,[CreatedOn]
			,[UpdatedOn]
			,[CreatedBy]
			,[UpdatedBy]
			,AuditAction, AuditTime   )
	SELECT  NEWID(), [Id]
           ,[FieldId]
           ,[OfferId]
           ,[Type]
           ,[Value]
           ,[TenantId]
           ,[IsActive]
           ,[CreatedOn]
           ,[UpdatedOn]
           ,[CreatedBy]
           ,[UpdatedBy]
			,@action, getdate()
	FROM deleted
End
'
go
