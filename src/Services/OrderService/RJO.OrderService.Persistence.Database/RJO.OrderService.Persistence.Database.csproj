<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="SQL\**" />
		<EmbeddedResource Include="SQL\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="MediatR" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\BuildingBlocks\RJO.MultiTenancyServer.EFCore\RJO.MultiTenancyServer.EFCore.csproj" />
		<ProjectReference Include="..\RJO.OrderService.Domain\RJO.OrderService.Domain.csproj" />
	</ItemGroup>

</Project>