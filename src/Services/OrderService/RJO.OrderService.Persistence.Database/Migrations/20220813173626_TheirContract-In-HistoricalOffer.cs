#nullable disable

using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class TheirContractInHistoricalOffer : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "TheirContract",
                schema: "Historical",
                table: "Offer",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "TheirContract",
                schema: "Historical",
                table: "Contract",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TheirContract",
                schema: "Historical",
                table: "Offer");

            migrationBuilder.AlterColumn<string>(
                name: "TheirContract",
                schema: "Historical",
                table: "Contract",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);
        }
    }
}
