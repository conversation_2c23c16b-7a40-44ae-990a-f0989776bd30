using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RJO.OrderService.Persistence.Database.Migrations
{
    /// <inheritdoc />
    public partial class OrderFillAudit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "OrderFill",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TransactionId = table.Column<string>(type: "nvarchar(40)", nullable: true),
                    ContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Matched = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    UtcTimestamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    InternalCode = table.Column<string>(type: "nvarchar(22)", nullable: true),
                    MarketTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    Number = table.Column<string>(type: "nvarchar(50)", nullable: true),
                    ErpMessage = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ErpStatus = table.Column<int>(type: "int", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    AuditAction = table.Column<string>(type: "nvarchar(6)", maxLength: 6, nullable: false),
                    AuditTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderFill", x => x.AuditId);
                });

			migrationBuilder.RunScripts("20250115_1");
		}

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.RunScripts("20250115_2");

			migrationBuilder.DropTable(
                name: "OrderFill",
                schema: "Audit");
		}
    }
}
