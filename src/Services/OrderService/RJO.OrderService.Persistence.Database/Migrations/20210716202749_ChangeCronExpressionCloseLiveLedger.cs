using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class ChangeCronExpressionCloseLiveLedger : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Settings",
                table: "TimerConfiguration",
                keyColumn: "Id",
                keyValue: new Guid("39d586ed-0a23-40dd-bdce-40c3c10ce16a"));

            migrationBuilder.UpdateData(
                schema: "Settings",
                table: "TimerConfiguration",
                keyColumn: "Id",
                keyValue: new Guid("63a42aef-c6c5-4403-af40-e349123e5d0f"),
                column: "CronExpression",
                value: "0 50 23 ? * *");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                schema: "Settings",
                table: "TimerConfiguration",
                keyColumn: "Id",
                keyValue: new Guid("63a42aef-c6c5-4403-af40-e349123e5d0f"),
                column: "CronExpression",
                value: "0 0 19/1 ? * *");

            migrationBuilder.InsertData(
                schema: "Settings",
                table: "TimerConfiguration",
                columns: new[] { "Id", "CreatedBy", "CreatedOn", "CronExpression", "IsActive", "Name", "UpdatedBy", "UpdatedOn" },
                values: new object[] { new Guid("39d586ed-0a23-40dd-bdce-40c3c10ce16a"), "system", new DateTime(2021, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "0 */15 * ? * *", true, "OfferBidsheetBasis", null, null });
        }
    }
}
