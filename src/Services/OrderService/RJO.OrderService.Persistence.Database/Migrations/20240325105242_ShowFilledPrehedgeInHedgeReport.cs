using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class ShowFilledPrehedgeInHedgeReport : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
		{ 
            migrationBuilder.CreateIndex(
                name: "IX_PreHedgeContract_ContractId",
                schema: "Transactions",
                table: "PreHedgeContract",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_PreHedgeContract_MarketTransactionId",
                schema: "Transactions",
                table: "PreHedgeContract",
                column: "MarketTransactionId");

			migrationBuilder.RunScripts("20240321");
		}

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PreHedgeContract_ContractId",
                schema: "Transactions",
                table: "PreHedgeContract");

            migrationBuilder.DropIndex(
                name: "IX_PreHedgeContract_MarketTransactionId",
                schema: "Transactions",
                table: "PreHedgeContract");

			migrationBuilder.RunScripts("20240211");
		}
    }
}
