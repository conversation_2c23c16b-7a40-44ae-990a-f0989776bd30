using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class AddNotificationMessage : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                schema: "Notifications",
                table: "Notification");

            migrationBuilder.DropColumn(
                name: "IsDismissed",
                schema: "Notifications",
                table: "Notification");

            migrationBuilder.RenameColumn(
                name: "IsSeen",
                schema: "Notifications",
                table: "Notification",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                schema: "Notifications",
                table: "Notification",
                newName: "CreatedOn");

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                schema: "Notifications",
                table: "Notification",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UpdatedBy",
                schema: "Notifications",
                table: "Notification",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedOn",
                schema: "Notifications",
                table: "Notification",
                type: "datetime2",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "NotificationMessage",
                schema: "Notifications",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ChannelType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AggregateType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AggregateId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Subject = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Body = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PhoneNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateDelivered = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateSeen = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateDismissed = table.Column<DateTime>(type: "datetime2", nullable: true),
                    NotificationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NotificationMessage", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NotificationMessage_Notification_NotificationId",
                        column: x => x.NotificationId,
                        principalSchema: "Notifications",
                        principalTable: "Notification",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationMessage_NotificationId",
                schema: "Notifications",
                table: "NotificationMessage",
                column: "NotificationId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "NotificationMessage",
                schema: "Notifications");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                schema: "Notifications",
                table: "Notification");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                schema: "Notifications",
                table: "Notification");

            migrationBuilder.DropColumn(
                name: "UpdatedOn",
                schema: "Notifications",
                table: "Notification");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                schema: "Notifications",
                table: "Notification",
                newName: "IsSeen");

            migrationBuilder.RenameColumn(
                name: "CreatedOn",
                schema: "Notifications",
                table: "Notification",
                newName: "CreatedAt");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                schema: "Notifications",
                table: "Notification",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsDismissed",
                schema: "Notifications",
                table: "Notification",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
