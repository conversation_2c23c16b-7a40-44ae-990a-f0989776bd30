using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class UpdateSettingErpParameterCommentLengthType : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Comments",
                schema: "ERP",
                table: "AgtraxSetting");

            migrationBuilder.DropColumn(
                name: "Comments",
                schema: "ERP",
                table: "AgrisSetting");

            migrationBuilder.AddColumn<int>(
                name: "CommentsLength",
                schema: "ERP",
                table: "AgtraxSetting",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "CommentsLength",
                schema: "ERP",
                table: "AgrisSetting",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CommentsLength",
                schema: "ERP",
                table: "AgtraxSetting");

            migrationBuilder.DropColumn(
                name: "CommentsLength",
                schema: "ERP",
                table: "AgrisSetting");

            migrationBuilder.AddColumn<string>(
                name: "Comments",
                schema: "ERP",
                table: "AgtraxSetting",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Comments",
                schema: "ERP",
                table: "AgrisSetting",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
