#nullable disable

using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class AddTheirContractField : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "TheirContract",
                schema: "Transactions",
                table: "Offer",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TheirContract",
                schema: "Transactions",
                table: "Contract",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TheirContract",
                schema: "Transactions",
                table: "Offer");

            migrationBuilder.DropColumn(
                name: "TheirContract",
                schema: "Transactions",
                table: "Contract");
        }
    }
}
