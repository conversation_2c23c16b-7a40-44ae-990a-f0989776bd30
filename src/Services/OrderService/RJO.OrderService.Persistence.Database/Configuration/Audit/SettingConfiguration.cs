using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.OrderService.Persistence.Database.Configuration.Setting;

namespace RJO.OrderService.Persistence.Database.Configuration.Audit;
public class SettingConfiguration : SettingBaseConfiguration<Domain.Audit.Setting>
{
	public override void Configure(EntityTypeBuilder<Domain.Audit.Setting> builder)
	{
		base.Configure(builder);
		builder.ToTable("Setting", SchemaName.Audit);
		builder.Property(x => x.AuditAction).IsRequired().HasMaxLength(6);
		builder.Property(x => x.AuditTime).IsRequired();
	}
}
