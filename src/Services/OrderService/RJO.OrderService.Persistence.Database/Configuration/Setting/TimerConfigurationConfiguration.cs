using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.OrderService.Domain;

namespace RJO.OrderService.Persistence.Database.Configuration.Setting;

public class TimerConfigurationConfiguration : IEntityTypeConfiguration<TimerConfiguration>
{
	public void Configure(EntityTypeBuilder<TimerConfiguration> builder)
	{
		builder.ConfigureBase();
		builder.ConfigureAuditable();
		builder.ToTable("TimerConfiguration", SchemaName.Setting);
		builder.Property(x => x.CronExpression).IsRequired().HasMaxLength(100);
		builder.Property(x => x.Name).IsRequired().HasMaxLength(50);
		builder.HasData(new
		{
			Id = new Guid("80F918C7-C681-4A89-96B9-1DD86033B7B3"),
			IsActive = true,
			Name = "RollOffersAndSpotBids",
			CronExpression = "5 0 ? * *",
			CreatedBy = "system",
			CreatedOn = new DateTime(2021, 1, 1)
		}, new
		{
			Id = new Guid("CB841359-F2FA-407D-B86B-B3077DCCCB84"),
			IsActive = true,
			Name = "CloseExpiredOffers",
			CronExpression = "30 13 ? * *",
			CreatedBy = "system",
			CreatedOn = new DateTime(2021, 1, 1)
		}, new
		{
			Id = new Guid("63A42AEF-C6C5-4403-AF40-E349123E5D0F"),
			IsActive = true,
			Name = "CloseLiveLedger",
			CronExpression = "1 0 ? * *",
			CreatedBy = "system",
			CreatedOn = new DateTime(2021, 1, 1)
		}, new
		{
			Id = new Guid("DCF555E0-536D-41E1-84FE-9802697798C6"),
			IsActive = true,
			Name = "LoadOaklandCustomers",
			CronExpression = "1 18 ? * *",
			CreatedBy = "system",
			CreatedOn = new DateTime(2021, 1, 1)
		});
	}
}
