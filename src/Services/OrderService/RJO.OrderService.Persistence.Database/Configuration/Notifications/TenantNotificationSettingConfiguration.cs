using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.OrderService.Domain.Notifications;

namespace RJO.OrderService.Persistence.Database.Configuration.Notifications;

public sealed class TenantNotificationSettingConfiguration : IEntityTypeConfiguration<TenantNotificationSetting>
{
	public void Configure(EntityTypeBuilder<TenantNotificationSetting> builder)
	{
		builder.ToTable("TenantNotificationSetting", SchemaName.Notification);
		
		builder.Property(tns => tns.EventType)
			.HasConversion<string>();
	}
}
