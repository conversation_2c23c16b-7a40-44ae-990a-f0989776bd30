using Hangfire; 
using LaunchDarkly.Sdk; 
using LaunchDarkly.Sdk.Server; 
using Magneto; 
using MediatR; 
using Microsoft.EntityFrameworkCore; 
using Microsoft.Extensions.DependencyInjection; 
using Microsoft.Extensions.Hosting; 
using Microsoft.Extensions.Logging; 
using RJO.BuildingBlocks.Common;
using RJO.OrderService.Domain.MultiTenancyServer;
using RJO.OrderService.Domain.Notifications; 
using RJO.OrderService.Persistence.Database; 
using RJO.OrderService.Services.Features.Notifications.Notifications.Commands; 
using RJO.OrderService.Services.Features.Notifications.Notifications.Jobs; 
using RJO.OrderService.Services.Features.Notifications.Notifications.Mapping; 
using RJO.OrderService.Services.Features.Notifications.Notifications.Queries;
using System.Collections.Concurrent;

namespace RJO.OrderService.Services.Features.Notifications.Services; 
 
public sealed class NotificationMessageProcessor(ILogger<NotificationMessageProcessor> logger, IServiceProvider serviceProvider, LdClient ldClient) : BackgroundService
{
	readonly PeriodicTimer _pollingInterval = new(30.Seconds());

	protected override async Task ExecuteAsync(CancellationToken stoppingToken) 
    { 
        // while (await _pollingInterval.WaitForNextTickAsync(stoppingToken)) 
        // { 
        //     try 
        //     { 
        //         var systemContext = User.WithKey("system"); 
        //         if (ldClient.BoolVariation(FeatureFlags.EnableNotifications, systemContext)) 
        //         { 
        //             await ProcessNotificationsAsync(stoppingToken); 
        //         } 
        //     } 
        //     catch (Exception ex) 
        //     { 
        //         logger.LogError(ex, "{ProcessorName}: An error occurred during processing: {ErrorMessage}", 
        //             nameof(NotificationMessageProcessor), ex.Message); 
        //     } 
        // } 
    }

	async Task ProcessNotificationsAsync(CancellationToken cancellationToken)
    {
        using var scope = serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var magneto = scope.ServiceProvider.GetRequiredService<IMagneto>();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
        var backgroundJobClient = scope.ServiceProvider.GetRequiredService<IBackgroundJobClient>();

        var tenants = await magneto.QueryAsync(new GetAllTenants(), CacheOption.Default, cancellationToken);

        foreach (var tenant in tenants)
        {
            await ProcessTenantNotificationsAsync(
                tenant, 
                dbContext, 
                mediator, 
                backgroundJobClient, 
                cancellationToken);
        }
    }

	async Task ProcessTenantNotificationsAsync(
        ApplicationTenant tenant,
        AppDbContext dbContext,
        IMediator mediator,
        IBackgroundJobClient backgroundJobClient,
        CancellationToken cancellationToken)
    {
        dbContext.TenancyContext.Tenant = tenant;

		logger.LogDebug("{ProcessorName}: Processing tenant: {TenantName}", 
            nameof(NotificationMessageProcessor), tenant.NormalizedCanonicalName);

        var undeliveredMessages = await dbContext.NotificationMessages
            .Where(nm => nm.DateDelivered == null)
            .ToListAsync(cancellationToken);

        logger.LogDebug("{ProcessorName}: Found {MessageCount} undelivered messages", 
            nameof(NotificationMessageProcessor), undeliveredMessages.Count);

        if (undeliveredMessages.Count == 0)
        {
            return;
        }

        var filteredMessages = DeduplicateSmsMessages(undeliveredMessages);

		// Track messages that were successfully delivered to update them at the end 
        var messagesToUpdate = new ConcurrentBag<Guid>();
        var messagesToRemove = new ConcurrentBag<Guid>();

        var messageTasks = filteredMessages.Select(message => 
            ProcessNotificationMessageAsync(
                message, 
                tenant, 
                mediator, 
                backgroundJobClient, 
                messagesToUpdate, 
                messagesToRemove, 
                cancellationToken));

        await Task.WhenAll(messageTasks);

        // Update the database with the results
        await ApplyMessageChangesAsync(
            dbContext, 
            messagesToUpdate, 
            messagesToRemove, 
            cancellationToken);

        logger.LogDebug("{ProcessorName}: Completed processing tenant: {TenantName}", 
            nameof(NotificationMessageProcessor), tenant.NormalizedCanonicalName);
    }

	async Task ProcessNotificationMessageAsync(
        NotificationMessage message,
        ApplicationTenant tenant,
        IMediator mediator,
        IBackgroundJobClient backgroundJobClient,
        ConcurrentBag<Guid> messagesToUpdate,
        ConcurrentBag<Guid> messagesToRemove,
        CancellationToken cancellationToken)
    {
        try
        {
            if (message.DateDelivered != null)
            {
                logger.LogDebug("{ProcessorName}: Skipping already delivered message: {MessageId}", 
                    nameof(NotificationMessageProcessor), message.Id);
                return;
            }

            switch (message.ChannelType)
            {
                case ChannelType.InApp:
                    await ProcessInAppNotificationAsync(
                        message, 
                        mediator, 
                        messagesToUpdate, 
                        cancellationToken);
                    break;

                case ChannelType.Email:
                    ProcessEmailNotification(
                        message, 
                        backgroundJobClient, 
                        messagesToUpdate, 
                        messagesToRemove);
                    break;

                case ChannelType.Sms:
                    await ProcessSmsNotificationAsync(
                        message, 
                        tenant, 
                        backgroundJobClient, 
                        messagesToUpdate, 
                        messagesToRemove, 
                        cancellationToken);
                    break;

                default:
                    throw new NotSupportedException($"Unsupported channel type: {message.ChannelType}");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "{ProcessorName}: Failed to deliver message: {MessageId}, error: {ErrorMessage}", 
                nameof(NotificationMessageProcessor), message.Id, ex.Message);
        }
    }

	static async Task ProcessInAppNotificationAsync(
        NotificationMessage message,
        IMediator mediator,
        ConcurrentBag<Guid> messagesToUpdate,
        CancellationToken cancellationToken)
    {
        var delivered = await mediator.Send(
            new SendInAppNotificationCommand(message.MapToResponse()), 
            cancellationToken);

        if (delivered)
        {
            messagesToUpdate.Add(message.Id);
        }
    }

	void ProcessEmailNotification(
        NotificationMessage message,
        IBackgroundJobClient backgroundJobClient,
        ConcurrentBag<Guid> messagesToUpdate,
        ConcurrentBag<Guid> messagesToRemove)
    {
        if (string.IsNullOrWhiteSpace(message.Email))
        {
            logger.LogWarning("{ProcessorName}: Email notification with MessageId {MessageId} has no email. Marking for deletion.", 
                nameof(NotificationMessageProcessor), message.Id);
            messagesToRemove.Add(message.Id);
            return;
        }

        // The EmailNotificationJob creates its own scope, so we can enqueue it directly
        backgroundJobClient.Enqueue<EmailNotificationJob>(job => 
            job.Perform(new(message.Email, message.Subject, message.Body), null));

        messagesToUpdate.Add(message.Id);
    }

	async Task ProcessSmsNotificationAsync(
        NotificationMessage message,
        ApplicationTenant tenant,
        IBackgroundJobClient backgroundJobClient,
        ConcurrentBag<Guid> messagesToUpdate,
        ConcurrentBag<Guid> messagesToRemove,
        CancellationToken cancellationToken)
    {
        // For SMS, use a separate scope to validate parameters before enqueueing the job
        using var messageScope = serviceProvider.CreateScope();
        var messageDbContext = messageScope.ServiceProvider.GetRequiredService<AppDbContext>();
        messageDbContext.TenancyContext.Tenant = tenant;

        if (string.IsNullOrWhiteSpace(message.PhoneNumber))
        {
            logger.LogWarning("{ProcessorName}: SMS notification with MessageId {MessageId} has no phone number. Marking for deletion.", 
                nameof(NotificationMessageProcessor), message.Id);
            messagesToRemove.Add(message.Id);
            return;
        }

        var notification = await messageDbContext.Notifications.FirstOrDefaultAsync(x => x.Id == message.NotificationId, cancellationToken);
            
        if (notification == null)
        {
            logger.LogWarning("{ProcessorName}: SMS notification with MessageId {MessageId} has invalid NotificationId. Marking for deletion.", 
                nameof(NotificationMessageProcessor), message.Id);
            messagesToRemove.Add(message.Id);
            return;
        }

        if (notification.EmployeeId != null)
        {
            var employee = await messageDbContext.Employees.FirstOrDefaultAsync(x => x.Id == notification.EmployeeId, cancellationToken);
                
            if (employee is { AllowSms: false })
            {
                logger.LogWarning("{ProcessorName}: SMS notification with MessageId {MessageId} isn't allowed by the employee. Marking for deletion.", 
                    nameof(NotificationMessageProcessor), message.Id);
                messagesToRemove.Add(message.Id);
                return;
            }
        }

        if (notification.CustomerId != null)
        {
            var customer = await messageDbContext.Customers.FirstOrDefaultAsync(x => x.Id == notification.CustomerId, cancellationToken);
                
            if (customer is { AllowSms: false })
            {
                logger.LogWarning("{ProcessorName}: SMS notification with MessageId {MessageId} isn't allowed by the customer. Marking for deletion.", 
                    nameof(NotificationMessageProcessor), message.Id);
                messagesToRemove.Add(message.Id);
                return;
            }
        }

        backgroundJobClient.Enqueue<SmsNotificationJob>(job => 
            job.Perform(new(
                tenant.Id, 
                notification.EmployeeId, 
                notification.CustomerId, 
                message.Id), 
                null));
            
        messagesToUpdate.Add(message.Id);
    }

	static async Task ApplyMessageChangesAsync(
        AppDbContext dbContext,
        ConcurrentBag<Guid> messagesToUpdate,
        ConcurrentBag<Guid> messagesToRemove,
        CancellationToken cancellationToken)
    {
        if (messagesToUpdate.IsEmpty && messagesToRemove.IsEmpty)
        {
            return;
        }

        // Update delivered messages
        if (!messagesToUpdate.IsEmpty)
        {
            var messagesToMarkAsDelivered = await dbContext.NotificationMessages
                .Where(nm => messagesToUpdate.Contains(nm.Id))
                .ToListAsync(cancellationToken);

            foreach (var message in messagesToMarkAsDelivered)
            {
                message.DateDelivered = DateTime.UtcNow;
            }
        }

        // Remove messages marked for deletion
        if (!messagesToRemove.IsEmpty)
        {
            var messagesToDelete = await dbContext.NotificationMessages
                .Where(nm => messagesToRemove.Contains(nm.Id))
                .ToListAsync(cancellationToken);

            dbContext.NotificationMessages.RemoveRange(messagesToDelete);
        }

        await dbContext.SaveChangesAsync(cancellationToken);
    }

	static List<NotificationMessage> DeduplicateSmsMessages(IReadOnlyCollection<NotificationMessage> messages) 
    { 
        var smsMessages = messages 
            .Where(nm => nm.ChannelType == ChannelType.Sms) 
            .GroupBy(nm => new { nm.Body, nm.PhoneNumber }) 
            .Select(g => g.First()); 
 
        var nonSmsMessages = messages 
            .Where(nm => nm.ChannelType != ChannelType.Sms); 
 
        return smsMessages.Concat(nonSmsMessages).ToList(); 
    } 
 
    public override void Dispose() 
    { 
        _pollingInterval.Dispose(); 
        base.Dispose(); 
    } 
}
