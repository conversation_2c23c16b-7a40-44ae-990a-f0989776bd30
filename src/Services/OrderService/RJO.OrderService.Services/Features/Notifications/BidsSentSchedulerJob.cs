using Hangfire;
using Hangfire.Server;
using Magneto;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.Notifications;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Bidsheet;
using RJO.OrderService.Services.DTO.RJO;
using RJO.OrderService.Services.Helper;
using System.Text;

namespace RJO.OrderService.Services.Features.Notifications;

#nullable enable

public sealed record BidsSentSchedulerJobOptions(Guid TenantId, string TenantDisplayName);

[Queue("notifications")]
[AutomaticRetry(Attempts = 3)]
public class BidsSentSchedulerJob
{
	readonly ILogger<BidsSentSchedulerJob> _logger;
	readonly IServiceProvider _serviceProvider;

	public BidsSentSchedulerJob(ILogger<BidsSentSchedulerJob> logger, IServiceProvider serviceProvider)
	{
		_logger = logger;
		_serviceProvider = serviceProvider;
	}

	public async Task Perform(BidsSentSchedulerJobOptions jobOptions, PerformContext? performContext)
	{
		// try
		// {
		// 	using var scope = _serviceProvider.CreateScope();
		//
		// 	var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
		// 	dbContext.TenancyContext.Tenant = new()
		// 	{
		// 		Id = jobOptions.TenantId,
		// 		DisplayName = nameof(BidsSentSchedulerJob)
		// 	};
		//
		// 	var magneto = scope.ServiceProvider.GetRequiredService<IMagneto>();
		// 	var futureMonthHelper = scope.ServiceProvider.GetRequiredService<FutureMonthsHelper>();
		//
		// 	var cashBids = await GetCashBidsAsync(dbContext, futureMonthHelper);
		// 	await SendNotificationAsync(magneto, cashBids, jobOptions.TenantDisplayName);
		// }
		// catch (Exception ex)
		// {
		// 	_logger.LogError(ex, "{JobName}: An error occurred while executing the job.", nameof(BidsSentSchedulerJob));
		// 	throw; // Re-throw the exception to ensure Hangfire can catch and retry.
		// }
	}

	async Task SendNotificationAsync(IMagneto magneto, IEnumerable<CashBidExtended> cashBids, string tenantDisplayName)
	{
		var (customerBids, employeeBids) = GroupCashBidsByCustomerOrEmployee(cashBids);

		foreach (var (customer, bidsForCustomer) in customerBids)
		{
			var inAppMessages = BuildInAppMessages(bidsForCustomer);

			var emailMessage = BuildEmailMessages(bidsForCustomer);
			_logger.LogInformation("{JobName}: Email message for customer {CustomerEmail} (CustomerId: {CustomerId}): {EmailMessage}", nameof(BidsSentSchedulerJob), customer.Email, customer.Id, emailMessage);

			var smsMessage = BuildSmsMessages(bidsForCustomer);
			_logger.LogInformation("{JobName}: SMS message for customer {CustomerEmail} (CustomerId: {CustomerId}): {SmsMessage}", nameof(BidsSentSchedulerJob), customer.Email, customer.Id, smsMessage);

			await magneto.CommandAsync(new UserNotificationManager(customer, UserType.Customer, "Bids", $"{tenantDisplayName} Bids", "Bids", inAppMessages, emailMessage, smsMessage, EventType.BidsSent, AggregateType.Bidsheet));
			_logger.LogInformation("{JobName}: Notification sent successfully for customer: {CustomerEmail} (CustomerId: {CustomerId})", nameof(BidsSentSchedulerJob), customer.Email, customer.Id);
		}

		foreach (var (employee, bidsForEmployee) in employeeBids)
		{
			var inAppMessages = BuildInAppMessages(bidsForEmployee);

			var emailMessage = BuildEmailMessages(bidsForEmployee);
			_logger.LogInformation("{JobName}: Email message for employee {EmployeeEmail} (EmployeeId: {EmployeeId}): {EmailMessage}", nameof(BidsSentSchedulerJob), employee.Email, employee.Id, emailMessage);

			var smsMessage = BuildSmsMessages(bidsForEmployee);
			_logger.LogInformation("{JobName}: SMS message for employee {EmployeeEmail} (EmployeeId: {EmployeeId}): {SmsMessage}", nameof(BidsSentSchedulerJob), employee.Email, employee.Id, smsMessage);

			await magneto.CommandAsync(new UserNotificationManager(employee, UserType.Employee, "Bids", $"{tenantDisplayName} Bids", "Bids", inAppMessages, emailMessage, smsMessage, EventType.BidsSent, AggregateType.Bidsheet));
			_logger.LogInformation("{JobName}: Notification sent successfully for employee: {EmployeeEmail} (EmployeeId: {EmployeeId})", nameof(BidsSentSchedulerJob), employee.Email, employee.Id);
		}
	}

	/// <summary>
	/// Groups cash bids by individual customers and employees based on their association with specific notification groups.
	/// This method changes the previous approach where we aggregated all cash bids into a single message for every user. Instead, it ensures that users only receive notifications for bids that are relevant to them.
	/// </summary>
	/// <param name="cashBids">The collection of all cash bids available for processing.</param>
	/// <returns>
	/// A tuple containing two dictionaries: 
	/// - CustomerBids: where each key is a UserNotificationDto for a customer and the value is a list of associated CashBidExtended objects.
	/// - EmployeeBids: where each key is a UserNotificationDto for an employee and the value is a list of associated CashBidExtended objects.
	/// </returns>
	static (Dictionary<UserNotificationDto, List<CashBidExtended>> CustomerBids, Dictionary<UserNotificationDto, List<CashBidExtended>> EmployeeBids) GroupCashBidsByCustomerOrEmployee(IEnumerable<CashBidExtended> cashBids)
	{
		var customerBids = new Dictionary<UserNotificationDto, List<CashBidExtended>>();
		var employeeBids = new Dictionary<UserNotificationDto, List<CashBidExtended>>();

		foreach (var bid in cashBids)
		{
			foreach (var customer in bid.Customers)
			{
				if (!customerBids.ContainsKey(customer))
					customerBids[customer] = new();
				customerBids[customer].Add(bid);
			}

			foreach (var employee in bid.Employees)
			{
				if (!employeeBids.ContainsKey(employee))
					employeeBids[employee] = new();
				employeeBids[employee].Add(bid);
			}
		}

		return (customerBids, employeeBids);
	}

	static string[] BuildInAppMessages(IEnumerable<CashBidExtended> cashBids)
	{
		var messages = cashBids.Select(bid => $"{bid.CommodityDisplayName}, {bid.DeliveryPeriod.Start:MM-dd-yy} to {bid.DeliveryPeriod.End:MM-dd-yy}, " +
											  $"Basis Price: {bid.BasisPrice.ToHrvystDecimalFormat()}, Cash Price: {bid.CashPrice?.ToHrvystDecimalFormat()}, Delivery Location: {bid.Location.Name}")
			.ToArray();

		return messages;
	}

	static string BuildEmailMessages(IEnumerable<CashBidExtended> cashBids)
	{
		var emailMessageBuilder = new StringBuilder();

		emailMessageBuilder.AppendLine(
			"<body style=\"font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 20px;\">" +
			"<div style=\"max-width: 800px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 10px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\">" +
			$"<h1 style=\"color: #333; margin-top: 0;\"><i>Cash bids for</i> {DateTime.Now:dddd, MMMM d, yyyy}</h1>"
		);

		foreach (var location in cashBids.OrderBy(bid => bid.Location.Name)
											.ThenBy(bid => bid.CommodityDisplayName)
											.ThenBy(bid => bid.DeliveryPeriod.Start)
											.GroupBy(bid => bid.Location.Name))
		{
			emailMessageBuilder.AppendLine($"<h2 style=\"margin: 0px\">{location.Key}</h2>");

			emailMessageBuilder.AppendLine(
				"<table style=\"width: 100%; border-collapse: collapse; margin-top: 5px; margin-bottom: 20px;\">" +
				"<thead>" +
				"<tr>" +
				"<th style=\"background-color: #f5f5f5; border: 1px solid #ddd; padding: 8px; text-align: left;\">Commodity</th>" +
				"<th style=\"background-color: #f5f5f5; border: 1px solid #ddd; padding: 8px; text-align: left;\">Delivery Dates</th>" +
				"<th style=\"background-color: #f5f5f5; border: 1px solid #ddd; padding: 8px; text-align: left;\">Basis</th>" +
				"<th style=\"background-color: #f5f5f5; border: 1px solid #ddd; padding: 8px; text-align: left;\">Cash Price</th>" +
				"</tr>" +
				"</thead>" +
				"<tbody>"
			);

			foreach (var commodityGroup in location.GroupBy(bid => bid.CommodityDisplayName))
			{
				foreach (var bid in commodityGroup)
				{
					emailMessageBuilder.AppendLine(
						"<tr>" +
						$"<td style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">{bid.CommodityDisplayName}</td>" +
						$"<td style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">{bid.DeliveryPeriod.Start:MM/dd/yy} - {bid.DeliveryPeriod.End:MM/dd/yy}</td>" +
						$"<td style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">{bid.BasisPrice.ToHrvystDecimalFormat()}</td>" +
						$"<td style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">{bid.CashPrice?.ToHrvystDecimalFormat()}</td>" +
						"</tr>"
					);
				}
			}
			emailMessageBuilder.AppendLine("</tbody></table>");
		}

		emailMessageBuilder.AppendLine("</div></body>");

		return emailMessageBuilder.ToString();
	}

	static string BuildSmsMessages(IEnumerable<CashBidExtended> cashBids)
	{
		var smsMessageBuilder = new StringBuilder();

		foreach (var location in cashBids.OrderBy(bid => bid.Location.Name).ThenBy(bid => bid.CommodityDisplayName).ThenBy(bid => bid.DeliveryPeriod.Start).GroupBy(bid => bid.Location.Name))
		{
			foreach (var commodityGroup in location.GroupBy(bid => bid.CommodityDisplayName))
			{
				smsMessageBuilder.AppendLine($"*{location.Key}   {commodityGroup.Key}*");

				foreach (var bid in commodityGroup.OrderBy(bid => bid.DeliveryPeriod.Start))
				{
					var startDate = bid.DeliveryPeriod.Start.ToString("MMM d");
					var endDate = bid.DeliveryPeriod.End.ToString("MMM d");
					var basisPrice = bid.BasisPrice.ToHrvystAlignDecimalFormat();
					var cashPrice = bid.CashPrice?.ToHrvystAlignDecimalFormat();
					smsMessageBuilder.AppendLine($"{startDate,-6} - {endDate,-6}   {basisPrice, 7}   {cashPrice, 7}");
				}
			}

			smsMessageBuilder.AppendLine();
		}

		return smsMessageBuilder.ToString();
	}

	async Task<IReadOnlyCollection<CashBidExtended>> GetCashBidsAsync(AppDbContext dbContext, FutureMonthsHelper futureMonthHelper)
	{
		var cashBids = await dbContext.SettingsBidsheets
			.AsNoTracking()
			.Include(x => x.Commodity)
				.ThenInclude(x => x.Product)
			.Include(x => x.DeliveryLocation)
			.Include(x => x.NotificationGroups)
			.Where(x => x.NotificationGroups.Any())
			.Select(x => new CashBidExtended
			{
				CommodityId = x.CommodityId,
				CommodityNumber = x.Commodity.Number,
				CommodityDisplayName = x.Commodity.Name,
				ExchangeSymbol = x.Commodity.Product.ExchangeSymbol,
				Symbol = $"{x.Commodity.Product.Code}{x.FutureMonth}",
				FutureMonth = FutureMonthsHelper.GetYearMonthFromFuturesMonth(x.FutureMonth),
				DeliveryPeriod = new()
				{
					Start = x.DeliveryStart,
					End = x.DeliveryEnd
				},
				DeliveryMonthCode = x.FutureMonth,
				ProductName = x.Commodity.Product.Code,
				BasisPrice = x.Basis,
				SymRoot = x.Commodity.Product.Code,
				BasisMonth = x.Delivery,
				Location = new()
				{
					Id = x.DeliveryLocationId,
					Number = x.DeliveryLocation.Number,
					Name = x.DeliveryLocation.Name
				},
				CropYear = x.CropYear,
				Employees = x.NotificationGroups
					.SelectMany(g => g.Employees)
					.Distinct()
					.Select(e => new UserNotificationDto(e.Id, e.PhoneNumber, e.Email))
					.ToList(),
				Customers = x.NotificationGroups
					.SelectMany(g => g.Customers)
					.Distinct()
					.Select(c => new UserNotificationDto(c.Id, c.PhoneNumber, c.Email))
					.ToList()
			})
			.ToListAsync();

		if (cashBids.Count == 0)
		{
			_logger.LogWarning("{JobName}: No cash bids found.", nameof(BidsSentSchedulerJob));
			return Array.Empty<CashBidExtended>();
		}

		var futurePrices = await GetFuturePricesAsync(cashBids, futureMonthHelper);
		if (futurePrices == null)
		{
			_logger.LogWarning("Future prices is null.");
			return Array.Empty<CashBidExtended>();
		}

		_logger.LogInformation("{JobName}: Retrieved future prices.", nameof(BidsSentSchedulerJob));

		var updatedCashBids = cashBids.Select(bid =>
		{
			var futurePrice = futurePrices.ResponseList.FirstOrDefault(x => x.Symbol == bid.ExchangeSymbol && x.MaturityDate[2..] == bid.FutureMonth);

			if (futurePrice?.TradeDate == null)
			{
				_logger.LogWarning("TradeDate is null for {Symbol}.", bid.ExchangeSymbol);
				return bid; // Return the bid unmodified if the future price data isn't valid.
			}

			var newFuturePrice = DataResponseExtension.CalculatePrice(futurePrice, DelayType.Delayed);
			return bid with
			{
				FuturePrice = newFuturePrice,
				FuturePriceDate = futurePrice.LastUpdatedTimestamp,
				FuturesChange = newFuturePrice - futurePrice.PrevSettledPrice,
				CashPrice = bid.BasisPrice + newFuturePrice
			};
		}).ToList();

		return updatedCashBids;
	}

	static Task<DataResponse?> GetFuturePricesAsync(IEnumerable<CashBidExtended> bidsheets, FutureMonthsHelper futureMonthHelper)
	{
		var symbols = bidsheets.Select(s => s.Symbol).Distinct().Aggregate((i, j) => $"{i},{j}");
		return futureMonthHelper.GetRawPriceFromRJO(symbols, PricePurpose.Display, DelayType.Delayed);
	}
}
