using System.Collections.Concurrent;
using System.Collections.Immutable;

namespace RJO.OrderService.Services.Features.Notifications.AutoRefresh;

#nullable enable

public class SignalRConnectionManager
{
	readonly ConcurrentDictionary<string, string> _connectedUsers = new(StringComparer.InvariantCultureIgnoreCase);

	public void AddUser(string email, string userId) => _connectedUsers.TryAdd(email, userId);

	public void RemoveUser(string email) => _connectedUsers.TryRemove(email, out _);

	public string? GetUserId(string email) => _connectedUsers.TryGetValue(email, out var userId) ? userId : null;
	
	public ImmutableArray<string> GetAllUserIds() => _connectedUsers.Values.ToImmutableArray();
}
