using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Domain;
using RJO.OrderService.Persistence.Database;

namespace RJO.OrderService.Services.Features.Notifications.AlertSounds;

#nullable enable

[Route("api/[controller]")]
[ApiController]
[ApiExplorerSettings(GroupName = "Notifications")]
public class AlertSoundsController : BaseController
{
	readonly AppDbContext _dbContext;

	public AlertSoundsController(AppDbContext dbContext) => _dbContext = dbContext;

	[HttpGet("{employeeIdOrEmail}")]
	[ProducesResponseType(typeof(Result<string?>), StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	public async Task<ActionResult<Result<string?>>> GetByEmployeeIdOrEmail([FromRoute] string employeeIdOrEmail, CancellationToken cancellationToken)
	{
		var employee = await FindEmployeeByIdOrEmail(employeeIdOrEmail, cancellationToken);

		if (employee == null)
		{
			throw new InvalidArgumentException($"Employee '{employeeIdOrEmail}' does not exist");
		}

		return Result(employee.AlertSound.ToString());
	}

	[HttpPost("{employeeIdOrEmail}/select")]
	[ProducesResponseType(typeof(Result<string>), StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	public async Task<ActionResult<Result<string>>> Create([FromRoute] string employeeIdOrEmail, [FromQuery] SoundRequest request, CancellationToken cancellationToken)
	{
		var employee = await FindEmployeeByIdOrEmail(employeeIdOrEmail, cancellationToken);

		if (employee == null)
		{
			throw new InvalidArgumentException($"Employee '{employeeIdOrEmail}' does not exist");
		}

		employee.AlertSound = request.AlertSound;

		await _dbContext.SaveChangesAsync(cancellationToken);
		
		return Result(employee.Id.ToString());
	}
	
	async Task<Employee?> FindEmployeeByIdOrEmail(string employeeIdOrEmail, CancellationToken cancellationToken)
	{
		Employee? employee;
		if (Guid.TryParse(employeeIdOrEmail, out var employeeId))
		{
			employee = await _dbContext.Employees.FindAsync(new object?[] { employeeId }, cancellationToken);
		}
		else
		{
			employee = await _dbContext.Employees.FirstOrDefaultAsync(e => e.Email == employeeIdOrEmail, cancellationToken);
		}

		return employee;
	}
}
