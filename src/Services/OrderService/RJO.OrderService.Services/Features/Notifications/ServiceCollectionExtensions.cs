using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using RJO.OrderService.Services.Features.Notifications;
using RJO.OrderService.Services.Features.Notifications.AutoRefresh;
using RJO.OrderService.Services.Features.Notifications.DailySummary;
using RJO.OrderService.Services.Features.Notifications.Hubs;
using RJO.OrderService.Services.Features.Notifications.Notifications.Commands;
using RJO.OrderService.Services.Features.Notifications.Notifications.Jobs;
using RJO.OrderService.Services.Features.Notifications.Services;

// ReSharper disable once CheckNamespace
namespace Microsoft.Extensions.DependencyInjection;

public static partial class ServiceCollectionExtensions
{
	public static IServiceCollection AddNotification(this IServiceCollection services, IConfiguration configuration, IHostEnvironment environment)
	{
		ArgumentNullException.ThrowIfNull(services);
		ArgumentNullException.ThrowIfNull(configuration);
		
		services.AddSingleton<PushNotificationSubscriptionManager>();
		services.AddSingleton<AzureEmailClient>();
		services.AddSingleton<AzureSmsClient>();
		services.AddSingleton<SignalRPushNotification>();
		services.AddSingleton<SignalRConnectionManager>();
		services.AddHostedService<NotificationMessageProcessor>();
		services.AddTransient<EmailNotificationJob>();
		services.AddTransient<SmsNotificationJob>();
		services.AddScoped<BidsSentSchedulerJob>();
		
		services.AddHostedService<RecurringJobsInitializer>();
		services.AddTransient<OfferExpiryCheckerJob>();

		services.AddTransient(typeof(IRequestHandler<,>), typeof(SendPushNotificationCommandHandler<>));

		if (environment.IsLocal())
			services.AddSignalR();
		else
			services.AddSignalR().AddAzureSignalR(configuration.AzureSignalRServiceConnectionString());
		
		return services;
	}

	public static IEndpointRouteBuilder MapNotification(this IEndpointRouteBuilder app)
	{
		ArgumentNullException.ThrowIfNull(app);
		
		app.MapHub<NotificationHub>(Constants.NotificationHubUrl);
		
		return app;
	}
}
