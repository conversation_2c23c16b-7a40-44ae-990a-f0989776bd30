using Hangfire;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Persistence.Repositories.Metadata;
using RJO.OrderService.Services.ErpIntegration;
using RJO.OrderService.Services.Events;
using RJO.OrderService.Services.Jobs;
using System.Collections.Concurrent;
using System.Net;

namespace RJO.OrderService.Services.Services.OrderProcess;

public class ErpIntegrationAgent
{
	readonly IBackgroundJobClient _backgroundJobClient;
	readonly LogWorkflowRepository _logWorkflow;
	readonly ContractRepository _contractRepository;
	readonly LocationRepository _locationRepository;
	readonly CustomerRepository _customerRepository;
	readonly CommodityRepository _commodityRepository;
	readonly TransactionTypeRepository _transactionTypeRepository;
	readonly ContractTypeRepository _contractTypeRepository;
	readonly EmployeeRepository _employeeRepository;
	readonly ProductRepository _productRepository;
	readonly SettingRepository _settingRepository;
	readonly AppDbContext _dbContext;
	readonly ErpIntegrationFactory _erpIntegrationFactory;
	readonly TenantSettingRepository _tenantSettingRepository;
	readonly ErpLogRepository _erpLogRepository;
	readonly OrderMetadataConfigurationRepository _orderMetadataConfigurationRepository;
	readonly ContractMetadataRepository _contractMetadataRepository;

	readonly OrderMetadataItemRepository _orderMetadataItemRepository;

	readonly ConcurrentDictionary<Guid, IErpIntegration> _erpIntegrations = new();
	static readonly ConcurrentDictionary<Guid, ErpToken> _tokens = new();
	readonly IServiceProvider _currentServiceProvider;

	public OrderMetadataConfigurationRepository OrderMetadataConfigurationRepository => _orderMetadataConfigurationRepository;
	public OrderMetadataItemRepository OrderMetaDataItemRepository => _orderMetadataItemRepository;
	public ContractMetadataRepository ContractMetadataRepository => _contractMetadataRepository;
	public ContractRepository ContractRepository => _contractRepository;
	public LocationRepository LocationRepository => _locationRepository;
	public CustomerRepository CustomerRepository => _customerRepository;
	public CommodityRepository CommodityRepository => _commodityRepository;
	public TransactionTypeRepository TransactionTypeRepository => _transactionTypeRepository;
	public ContractTypeRepository ContractTypeRepository => _contractTypeRepository;
	public EmployeeRepository EmployeeRepository => _employeeRepository;
	public ProductRepository ProductRepository => _productRepository;
	public SettingRepository SettingRepository => _settingRepository;
	public ErpLogRepository ErpLogRepository => _erpLogRepository;
	public AppDbContext DbContext => _dbContext;
	public IServiceProvider ServiceProvider => _currentServiceProvider;
	public Guid TenantId => _dbContext.TenantId;
	public TenantSettingRepository TenantSettingRepository => _tenantSettingRepository;
	public static ConcurrentDictionary<Guid, ErpToken> Tokens => _tokens;
	public IHttpClientFactory HttpClientFactory { get; set; }

	public ErpIntegrationAgent(
		IBackgroundJobClient backgroundJobClient,
		LogWorkflowRepository logWorkflow,
		ContractRepository contractRepository,
		LocationRepository locationRepository,
		CustomerRepository customerRepository,
		CommodityRepository commodityRepository,
		TransactionTypeRepository transactionTypeRepository,
		ContractTypeRepository contractTypeRepository,
		EmployeeRepository employeeRepository,
		ProductRepository productRepository,
		SettingRepository settingRepository,
		TenantSettingRepository tenantSettingRepository,
		ErpLogRepository erpLogRepository,
		OrderMetadataConfigurationRepository orderMetadataConfigurationRepository,
		OrderMetadataItemRepository orderMetadataItemRepository,
		ContractMetadataRepository contractMetadataRepository,
		IServiceProvider serviceProvider,
		IHttpClientFactory httpClientFactory,
		AppDbContext dbContext)
	{
		_backgroundJobClient = backgroundJobClient;
		_logWorkflow = logWorkflow;
		_contractRepository = contractRepository;
		_locationRepository = locationRepository;
		_customerRepository = customerRepository;
		_commodityRepository = commodityRepository;
		_transactionTypeRepository = transactionTypeRepository;
		_contractTypeRepository = contractTypeRepository;
		_employeeRepository = employeeRepository;
		_productRepository = productRepository;
		_settingRepository = settingRepository;
		_tenantSettingRepository = tenantSettingRepository;
		_erpLogRepository = erpLogRepository;
		_dbContext = dbContext;
		_orderMetadataConfigurationRepository = orderMetadataConfigurationRepository;
		_orderMetadataItemRepository = orderMetadataItemRepository;
		_contractMetadataRepository = contractMetadataRepository;
		_erpIntegrationFactory = new();
		_currentServiceProvider = serviceProvider;
		HttpClientFactory = httpClientFactory;
	}

	public async Task<ErpContractStatus> ProcessNewContract(Contract contract)
	{
		if (string.Equals(contract.Event, EContractEvent.Price.ToString(), StringComparison.Ordinal))
			return await ProcessPricing(contract);
		else
			return await ProcessCreation(contract);
	}

	public async Task<ErpContractStatus> ProcessCreation(Contract contract)
	{
		_logWorkflow.SetContract(contract.Id);
		await _logWorkflow.AddLog("ERP ProcessCreation", "Process starts");
		var status = new ErpContractStatus();
		{
			status.IntegrationGuid = contract.InternalCode;
			status.TenantId = TenantId;
		}
		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = contract.InternalCode,
			ErpAction = ErpAction.Create,
			ContractStatus = status
		};

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
			{
				status.ErpStatus = ErpStatus.NoErp;
				await _logWorkflow.AddLog("ERP ProcessCreation", "No ERP Status");
			}
			else
			{
				await _logWorkflow.AddLog("ERP ProcessCreation", $"Process sends to {erp.Name}");
				erpResponse = await erp.CreateContract(contract);
				status.ContractNumber = erpResponse.ContractNumber;
				status.ErpStatus = erpResponse.ErpStatus;
				status.ErrorMessage = string.Join(';', erpResponse.ErrorList);
				if (status.ErpStatus == ErpStatus.None)
					return status;

				erpEvent.RequestPayload = erpResponse.RequestPayload;
				erpEvent.ResponsePayload = erpResponse.ResponsePayload;
				erpEvent.ErpName = erp.Name;
			}
		}
		catch (Exception ed)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErrorMessage = ed.Message;
			if (ed is WebException)
			{
				var isTimedOut = ((WebException)ed).Status == WebExceptionStatus.Timeout || ((WebException)ed).Status == WebExceptionStatus.ConnectFailure;
				status.ErpStatus = isTimedOut ? ErpStatus.TimeOut : ErpStatus.Fail;
			}
			else
			{
				status.ErpStatus = ErpStatus.Fail;
			}
		}

		SendChangeErpStatusEvent(erpEvent);
		await _logWorkflow.AddLog("ERP ProcessCreation", $"Process ends: Status {status.ErpStatus}, Message {status.ErrorMessage}");
		return status;
	}

	public async Task<ErpContractStatus> ProcessUpdate(Contract contract, decimal quantityChanged = 0, Contract originalContract = null)
	{
		_logWorkflow.SetContract(contract.Id);
		await _logWorkflow.AddLog("ERP ProcessUpdate", $"Process starts: QuantityChanged {quantityChanged}");
		if ((contract.ErpStatus == ErpStatus.Fail || contract.ErpStatus == ErpStatus.TimeOut || contract.ErpStatus == ErpStatus.NoErp || contract.ErpStatus == ErpStatus.NotSent)
			&& contract.ContractTypeId != ContractTypeDictionary.NTC && contract.Source != ContractSource.Migrated
		   )
		{
			var last = await GetLatestErpStatus(contract.Id);
			if (last != null)
			{
				switch (last.Action)
				{
					case ErpAction.Create:
						await _logWorkflow.AddLog("ERP ProcessUpdate", " Redirect to ProcessCreation");
						return await ProcessCreation(contract);
					case ErpAction.Price:
						await _logWorkflow.AddLog("ERP ProcessUpdate", " Redirect to ProcessPricing");
						return await ProcessPricing(contract);
					case ErpAction.Roll:
						await _logWorkflow.AddLog("ERP ProcessUpdate", " Redirect to ProcessRolling");
						return await ProcessRolling(contract);
					case ErpAction.Split:
						await _logWorkflow.AddLog("ERP ProcessUpdate", " Redirect to ProcessSplit");
						return await ProcessSplit(contract);
					case ErpAction.Conversion:
						await _logWorkflow.AddLog("ERP ProcessUpdate", " Redirect to ProcessNtcConversion");
						return await ProcessNtcConversion(contract);
				}
			}
		}

		// Edit / update
		var status = new ErpContractStatus();
		{
			status.IntegrationGuid = contract.InternalCode;
			status.TenantId = TenantId;
		}
		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = contract.InternalCode,
			ErpAction = ErpAction.Update,
			ContractStatus = status
		};
		var shouldSendErpChangeEvent = true;

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
			{
				status.ErpStatus = ErpStatus.NoErp;
				await _logWorkflow.AddLog("ERP ProcessUpdate", "No ERP Status");
			}
			else if (originalContract != null && !erp.IsErpUpdateRequired(contract, originalContract)) 
			{
				// nothing changed
				await _logWorkflow.AddLog("ERP ProcessUpdate", "No ERP Changed");
				shouldSendErpChangeEvent = false;
			}
			else
			{
				await _logWorkflow.AddLog("ERP ProcessUpdate", $"Process sends to {erp.Name}");
				erpResponse = await erp.UpdateContract(contract, quantityChanged);
				status.ContractNumber = erpResponse.ContractNumber;
				status.ErpStatus = erpResponse.ErpStatus;
				status.ErrorMessage = string.Join(';', erpResponse.ErrorList);
				if (status.ErpStatus == ErpStatus.None)
					return status;

				erpEvent.RequestPayload = erpResponse.RequestPayload;
				erpEvent.ResponsePayload = erpResponse.ResponsePayload;
				erpEvent.ErpName = erp.Name;
			}
		}
		catch (Exception ed)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErpStatus = ErpStatus.Fail;
			status.ErrorMessage = ed.Message;
		}

		if (shouldSendErpChangeEvent)
			SendChangeErpStatusEvent(erpEvent);
		await _logWorkflow.AddLog("ERP ProcessUpdate", $"Process ends: Status {status.ErpStatus}, Message {status.ErrorMessage}");
		return status;
	}

	public async Task<ErpContractStatus> ProcessPricing(Contract contract)
	{
		_logWorkflow.SetContract(contract.Id);
		await _logWorkflow.AddLog("ERP ProcessPricing", "Process starts");
		var status = new ErpContractStatus();
		{
			status.IntegrationGuid = contract.InternalCode;
			status.TenantId = TenantId;
		}
		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = contract.InternalCode,
			ErpAction = ErpAction.Price,
			ContractStatus = status
		};

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
			{
				status.ErpStatus = ErpStatus.NoErp;
				await _logWorkflow.AddLog("ERP ProcessPricing", "No ERP Status");
			}
			else
			{
				await _logWorkflow.AddLog("ERP ProcessPricing", $"Process sends to {erp.Name}");
				var parentContract = await _contractRepository.GetById(contract.ParentId.Value);
				erpResponse = await erp.PriceContract(contract, parentContract);
				status.ContractNumber = erpResponse.ContractNumber;
				status.ErpStatus = erpResponse.ErpStatus;
				status.ErrorMessage = string.Join(';', erpResponse.ErrorList);
				if (status.ErpStatus == ErpStatus.None)
					return status;

				erpEvent.RequestPayload = erpResponse.RequestPayload;
				erpEvent.ResponsePayload = erpResponse.ResponsePayload;
				erpEvent.ErpName = erp.Name;
			}
		}
		catch (Exception ed)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErpStatus = ErpStatus.Fail;
			status.ErrorMessage = ed.Message;
		}

		SendChangeErpStatusEvent(erpEvent);
		await _logWorkflow.AddLog("ERP ProcessPricing", $"Process ends: Status {status.ErpStatus}, Message {status.ErrorMessage}");
		return status;
	}

	public async Task<ErpContractStatus> ProcessRolling(Contract contract)
	{
		_logWorkflow.SetContract(contract.Id);
		await _logWorkflow.AddLog("ERP ProcessRolling", "Process starts");
		var status = new ErpContractStatus();
		{
			status.IntegrationGuid = contract.InternalCode;
			status.TenantId = TenantId;
		}
		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = contract.InternalCode,
			ErpAction = ErpAction.Roll,
			ContractStatus = status
		};

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
			{
				status.ErpStatus = ErpStatus.NoErp;
				await _logWorkflow.AddLog("ERP ProcessRolling", "No ERP Status");
			}
			else
			{
				await _logWorkflow.AddLog("ERP ProcessRolling", $"Process sends to {erp.Name}");
				var parentContract = await _contractRepository.GetById(contract.ParentId.Value);
				erpResponse = await erp.RollContract(contract, parentContract);
				status.ContractNumber = erpResponse.ContractNumber;
				status.ErpStatus = erpResponse.ErpStatus;
				status.ErrorMessage = string.Join(';', erpResponse.ErrorList);
				if (status.ErpStatus == ErpStatus.None)
					return status;

				erpEvent.RequestPayload = erpResponse.RequestPayload;
				erpEvent.ResponsePayload = erpResponse.ResponsePayload;
				erpEvent.ErpName = erp.Name;
			}
		}
		catch (Exception ed)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErpStatus = ErpStatus.Fail;
			status.ErrorMessage = ed.Message;
		}

		SendChangeErpStatusEvent(erpEvent);
		await _logWorkflow.AddLog("ERP ProcessRolling", $"Process ends: Status {status.ErpStatus}, Message {status.ErrorMessage}");
		return status;
	}

	public async Task<ErpContractStatus> ProcessSplit(Contract contract)
	{
		_logWorkflow.SetContract(contract.Id);
		await _logWorkflow.AddLog("ERP ProcessSplit", "Process starts");
		var status = new ErpContractStatus();
		{
			status.IntegrationGuid = contract.InternalCode;
			;
			status.TenantId = TenantId;
		}
		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = contract.InternalCode,
			ErpAction = ErpAction.Split,
			ContractStatus = status
		};

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
			{
				status.ErpStatus = ErpStatus.NoErp;
				await _logWorkflow.AddLog("ERP ProcessSplit", "No ERP Status");
			}
			else
			{
				await _logWorkflow.AddLog("ERP ProcessSplit", $"Process sends to {erp.Name}");
				var parentContract = await _contractRepository.GetById(contract.ParentId.Value);
				erpResponse = await erp.SplitContract(contract, parentContract);
				status.ContractNumber = erpResponse.ContractNumber;
				status.ErpStatus = erpResponse.ErpStatus;
				status.ErrorMessage = string.Join(';', erpResponse.ErrorList);
				if (status.ErpStatus == ErpStatus.None)
					return status;

				erpEvent.RequestPayload = erpResponse.RequestPayload;
				erpEvent.ResponsePayload = erpResponse.ResponsePayload;
				erpEvent.ErpName = erp.Name;
			}
		}
		catch (Exception ed)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErpStatus = ErpStatus.Fail;
			status.ErrorMessage = ed.Message;
		}

		SendChangeErpStatusEvent(erpEvent);
		await _logWorkflow.AddLog("ERP ProcessSplit", $"Process ends: Status {status.ErpStatus}, Message {status.ErrorMessage}");
		return status;
	}

	public async Task<ErpContractStatus> ProcessAdjust(Contract contract, decimal quantity, bool updateParent = true, bool? appliedLoad = null)
	{
		_logWorkflow.SetContract(contract.Id);
		await _logWorkflow.AddLog("ERP ProcessAdjust", "Process starts");
		var status = new ErpContractStatus();
		{
			status.IntegrationGuid = contract.InternalCode;
			status.TenantId = TenantId;
		}
		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = contract.InternalCode,
			ErpAction = ErpAction.Cancel,
			ContractStatus = status
		};

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
			{
				status.ErpStatus = ErpStatus.NoErp;
				await _logWorkflow.AddLog("ERP ProcessAdjust", "No ERP Status");
			}
			else
			{
				await _logWorkflow.AddLog("ERP ProcessAdjust", $"Process sends to {erp.Name}");
				erpResponse = await erp.AdjustContract(contract, quantity, updateParent, appliedLoad);
				status.ContractNumber = erpResponse.ContractNumber;
				status.ErpStatus = erpResponse.ErpStatus;
				status.ErrorMessage = string.Join(';', erpResponse.ErrorList);
				if (status.ErpStatus == ErpStatus.None)
					return status;

				erpEvent.RequestPayload = erpResponse.RequestPayload;
				erpEvent.ResponsePayload = erpResponse.ResponsePayload;
				erpEvent.ErpName = erp.Name;
			}
		}
		catch (Exception ed)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErpStatus = ErpStatus.Fail;
			status.ErrorMessage = ed.Message;
		}

		SendChangeErpStatusEvent(erpEvent);
		await _logWorkflow.AddLog("ERP ProcessAdjust", $"Process ends: Status {status.ErpStatus}, Message {status.ErrorMessage}");
		return status;
	}

	protected async Task<ErpLog> GetLatestErpStatus(Guid contractId)
	{
		var query = from erpLog in await _erpLogRepository.GetAllEntities()
					where erpLog.Action.StartsWith("Request.") && erpLog.ContractId == contractId
					orderby erpLog.CreatedOn descending
					select erpLog;
		var last = query.FirstOrDefault();
		return last;
	}

	public async Task<bool> AnyErpReply(ErpLog erpRec)
	{
		var query = from erpLog in await _erpLogRepository.GetAllEntities()
					where erpLog.Action.StartsWith("Reply.") && erpLog.ContractId == erpRec.ContractId
															 && erpLog.CreatedOn > erpRec.CreatedOn
					select erpLog;
		return query.Any();
	}

	public async Task<ErpContractStatus> ProcessResend(Contract contract)
	{
		_logWorkflow.SetContract(contract.Id);
		await _logWorkflow.AddLog("ERP Resend", "Process starts");

		var status = new ErpContractStatus
		{
			InternalCode = contract.InternalCode,
			TenantId = TenantId
		};
		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = contract.InternalCode,
			ContractStatus = status
		};

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
			{
				status.ErpStatus = ErpStatus.NoErp;
				await _logWorkflow.AddLog("ERP ProcessResend", "No ERP Status");
				return status;
			}

			var last = await GetLatestErpStatus(contract.Id);
			AssertionConcern.ArgumentIsNotNull(last, "Contract was not found.");

			await _logWorkflow.AddLog("ERP ProcessResend", $"Process sends to {erp.Name}");
			erpEvent.ErpAction = last.Action;
			erpEvent.RequestPayload = last.RequestPayload;
			erpEvent.ErpName = erp.Name;
			erpResponse = await erp.ResendContract(contract, last.Action, last.RequestPayload);

			status.ContractNumber = erpResponse.ContractNumber;
			status.ScheduleNumber = erpResponse.ScheduleNumber;
			status.ErpStatus = erpResponse.ErpStatus;
			status.ErrorMessage = string.Join(';', erpResponse.ErrorList);

			erpEvent.ResponsePayload = erpResponse.ResponsePayload;
			SendChangeErpStatusEvent(erpEvent);

			await _logWorkflow.AddLog("ERP ProcessResend", $"Process ends: Status {status.ErpStatus}, Message {status.ErrorMessage}");
		}
		catch (Exception ex)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErpStatus = ErpStatus.Fail;
			status.ErrorMessage = ex.Message;
			throw;
		}

		await _logWorkflow.AddLog("ERP ProcessResend", $"Process ends: Status {status.ErpStatus}, Message {status.ErrorMessage}");
		return status;
	}

	public async Task<TenantSetting> GetTenantSetting()
	{
		var tenantSetting = await _tenantSettingRepository.GetSingleOrDefault();
		if (tenantSetting == null || string.IsNullOrEmpty(tenantSetting.ErpName))
			return null;
		return tenantSetting;
	}

	public async Task<IErpIntegration> GetErpIntegration()
	{
		var tenantSetting = await _tenantSettingRepository.GetSingleOrDefault();
		if (tenantSetting == null || string.IsNullOrEmpty(tenantSetting.ErpName) || !tenantSetting.IsActive)
			return null;

		var idString = tenantSetting.Id.ToString();
		if (_erpIntegrations.ContainsKey(tenantSetting.Id))
			return _erpIntegrations[tenantSetting.Id];

		var erp = _erpIntegrationFactory.CreateErpIntegraion(this, tenantSetting.ErpName);
		_erpIntegrations.AddOrUpdate(tenantSetting.Id, k => erp, (k, v) => erp);

		return erp;
	}

	public async Task<ErpInventoryResponse> GetInventory(Guid customerId)
	{
		var erp = await GetErpIntegration();
		return await erp.GetInventory(customerId);			
	}

	public void SendChangeErpStatusEvent(ErpStatusChangeEvent erpEvent) =>
		_backgroundJobClient.Enqueue<ErpStatusChangeJob>(x => x.Perform(new()
		{
			ErpContractEvent = erpEvent,
			TenantId = erpEvent.ContractStatus.TenantId
		}, null));

	async Task<Dictionary<string, object>> GetContractMeta(Contract contract)
	{
		var queryResult =
		   (from m in await _contractMetadataRepository.GetAllEntities()
			join mc in await _orderMetadataConfigurationRepository.GetAllEntities() on m.FieldId.Value equals mc.Id
			join i in await _orderMetadataItemRepository.GetAllEntities()
				on new
				{
					ConfigId = mc.Id,
					KeyValue = m.Value
				} equals new
				{
					ConfigId = i.ConfigurationId,
					KeyValue = i.Value
				} into mvalue
			from i in mvalue.DefaultIfEmpty()
			where m.ContractId == contract.Id // && m.Type.As == "Field"
				  && m.IsActive == true
				  && mc.ErpField != null
				  && mc.IsActive == true
			select new
			{
				mc.ErpField,
				ErpType = mc.Type,
				ErpValue = i.ErpValue != null ? i.ErpValue : m.Value
			}
		   ).ToList();
		var result = new Dictionary<string, object>();
		foreach (var x in queryResult)
		{
			if (x.ErpType == EOrderMetadataFieldType.Numeric)
			{
				double val = 0.0;
				if (!string.IsNullOrEmpty(x.ErpValue) && !double.TryParse(x.ErpValue, out val))
					throw new BusinessException($"Invalid value {x.ErpValue} for customer field {x.ErpField} when building Erp request");
				result.Add(x.ErpField, val);
			}
			else
				result.Add(x.ErpField, x.ErpValue);
		}
		return result;
	}

	public async Task BuildStandardRequestOnCreate(Contract contract, ErpContract erpContract)
	{
		// ContractLocation / Location
		var locationQuery = from l in await _locationRepository.GetAllEntities()
							select new
							{
								LocationId = l.Id,
								l.Number
							};
		var contractLocation = locationQuery.FirstOrDefault(x => x.LocationId == contract.LocationId).Number;

		// InventoryLocation / DeliveryLocation
		var deliveryLocation = locationQuery.FirstOrDefault(x => x.LocationId == contract.DeliveryLocationId).Number;

		string contractRegion = "";
		if (contract.RegionId != null && contract.RegionId != Guid.Empty)
		{
			var region = await _dbContext.Regions.AsNoTracking().FirstOrDefaultAsync(x => x.Id == contract.RegionId);
			contractRegion = region.ErpNumber;
		}

		//ContractType
		var contractTypeQuery = from ct in await _contractTypeRepository.GetAllEntities()
								select new
								{
									ContractTypeId = ct.Id,
									ct.Code
								};
		var contractTypeQuery2 = contractTypeQuery.FirstOrDefault(x => x.ContractTypeId == contract.ContractTypeId);
		var contractType = contractTypeQuery2.Code == "FlatPrice" ? "P" : contractTypeQuery2.Code == "HTA" ? "F" : contractTypeQuery2.Code == "Basis" ? "B" : null;
		var contractTypeCode = contractTypeQuery2.Code;

		// Commodity
		var commodityQuery = from c in await _commodityRepository.GetAllEntities()
							 select new
							 {
								 CommodityId = c.Id,
								 c.Number
							 };
		var commodity = commodityQuery.FirstOrDefault(x => x.CommodityId == contract.CommodityId).Number;

		// NameID
		var customerQuery = from c in await _customerRepository.GetAllEntities()
							select new
							{
								customerId = c.Id,
								c.Number
							};
		var nameID = customerQuery.FirstOrDefault(x => x.customerId == contract.CustomerId).Number;

		// BoardName
		var productQuery = from c in await _commodityRepository.GetAllEntities()
						   join p in await _productRepository.GetAllEntities() on c.ProductId equals p.Id
						   select new
						   {
							   CommodityId = c.Id,
							   ProductId = p.Id,
							   ProductCode = p.Code,
							   p.Number
						   };
		var prodRec = productQuery.FirstOrDefault(x => x.CommodityId == contract.CommodityId);
		var boardName = prodRec.ProductCode;

		// UserId
		var employeeQuery = from e in await _employeeRepository.GetAllEntities()
							select new
							{
								employeeId = e.Id,
								e.Number
							};
		var userId = employeeQuery.FirstOrDefault(x => x.employeeId == contract.UpdatedEmployeeId).Number;

		erpContract.PurchaseSales = contract.IsSell ? "S" : "P";
		erpContract.ContractRegion = contractRegion;
		erpContract.ContractLocation = contractLocation;
		erpContract.ContractType = contractType;
		erpContract.ContractTypeCode = contractTypeCode;
		erpContract.Commodity = commodity;
		erpContract.NameID = nameID;
		erpContract.ScheduledQuantity = contractType == "P" || contract.Status.Value == EContractState.Priced
			? contract.Quantity.ToString()
			: contract.GrossRemainingBalance.ToString();
		erpContract.IntegrationGuid = contract.InternalCode;
		erpContract.BoardName = boardName;
		erpContract.Futuresmonth = contract.FuturesMonth;
		erpContract.Deliverydate = contract.DeliveryStartDate;
		erpContract.DueDate = contract.DeliveryEndDate;
		erpContract.ContractPrice = contract.Price.ToString();
		erpContract.FuturesPrice = contract.FuturesPrice.ToString();
		erpContract.BasisPrice = contract.NetBasis.ToString();
		erpContract.FreightPrice = contract.FreightPrice;
		erpContract.UserId = userId;
		erpContract.Fees1 = contract.Fees1;
		erpContract.Fees2 = contract.Fees2;
		erpContract.CreationDate = contract.CreatedOn;
		erpContract.EmployeeNumber = userId;
		erpContract.CustomerNumber = nameID;
		erpContract.CropYear = contract.CropYear;
		erpContract.PriceStatus = contract.Status.ToString();
		erpContract.Comments = contract.Comments;
		erpContract.TenantId = TenantId;
		erpContract.InventoryLocation = deliveryLocation;
		erpContract.ContractNumber = contract.Number;
		// If the transaction has a negative freight value, delivery terms should be coded to say "FOB" and sent at the schedule level, not the header.
		// If transaction has a positive freight value or no freight, delivery terms should be coded to say Delvd
		erpContract.FreightCode = contract.FreightPrice < 0 ? "FOB" : "DELVD";
		erpContract.TheirContract = contract.TheirContract ?? "";
		erpContract.CustomFields = await GetContractMeta(contract);
	}

	public async Task BuildStandardRequestOnUpdate(Contract contract, ErpContract erpContract)
	{
		var query = from f in await _contractRepository.GetAllEntities()
					join b in await _employeeRepository.GetAllEntities() on f.UpdatedEmployeeId equals b.Id
					join cu in await _customerRepository.GetAllEntities() on f.CustomerId equals cu.Id into gcu
					from customer in gcu.DefaultIfEmpty()
					join r in _dbContext.Regions.AsNoTracking() on f.RegionId equals r.Id into gr
					from region in gr.DefaultIfEmpty()
					join l in await _locationRepository.GetAllEntities() on f.LocationId equals l.Id
					join dl in await _locationRepository.GetAllEntities() on f.DeliveryLocationId equals dl.Id
					join c in await _commodityRepository.GetAllEntities() on f.CommodityId equals c.Id
					join pr in await _productRepository.GetAllEntities() on c.ProductId equals pr.Id
					join t in await _transactionTypeRepository.GetAllEntities() on f.TransactionTypeId equals t.Id
					join ct in await _contractTypeRepository.GetAllEntities() on f.ContractTypeId equals ct.Id
					join ff in await _contractRepository.GetAllEntities() on f.ParentId equals ff.Id into gff
					from parent in gff.DefaultIfEmpty()
					select new
					{
						ContractId = f.Id,
						PurchaseSales = f.IsSell ? "S" : "P",
						RegionNumber = region.ErpNumber,
						LocationName = l.Number,
						DeliveryLocationName = dl.Number,
						ContractType = ct.Code == "FlatPrice" ? "P" : ct.Code == "HTA" ? "F" : ct.Code == "Basis" ? "B" : null,
						ContractTypeCode = ct.Code,
						CommodityName = c.Number,
						Customer = customer.Number,
						f.Quantity,
						f.RemainingBalance,
						f.GrossRemainingBalance,
						ContractNumber = f.Number,
						ProductCode = pr.Code,
						f.FuturesMonth,
						DeliveryDate = f.DeliveryStartDate,
						DueDate = f.DeliveryEndDate,
						f.Price,
						f.FuturesPrice,
						BasisPrice = f.NetBasis,
						TranCode1 = f.Fees1,
						TranCode2 = f.Fees2,
						f.FreightPrice,
						EmployeeNumber = b.Number,
						f.Status
					};
		var item = query.AsNoTracking().FirstOrDefault(x => x.ContractId == contract.Id);
		erpContract.PurchaseSales = item.PurchaseSales;
		erpContract.ContractRegion = item.RegionNumber;
		erpContract.ContractLocation = item.LocationName;
		erpContract.ContractType = item.ContractType;
		erpContract.ContractTypeCode = item.ContractTypeCode;
		erpContract.Commodity = item.CommodityName;
		erpContract.NameID = item.Customer == null ? null : item.Customer;
		erpContract.ScheduledQuantity = item.ContractType == "P" || item.Status.Value == EContractState.Priced
			? item.Quantity.ToString()
			: item.GrossRemainingBalance.ToString();
		erpContract.IntegrationGuid = item.ContractId.ToString();
		erpContract.BoardName = item.ProductCode;
		erpContract.Futuresmonth = item.FuturesMonth;
		erpContract.Deliverydate = item.DeliveryDate;
		erpContract.DueDate = item.DueDate;
		erpContract.ContractPrice = item.Price.ToString();
		erpContract.FuturesPrice = item.FuturesPrice.ToString();
		erpContract.BasisPrice = item.BasisPrice.ToString();
		erpContract.FreightPrice = item.FreightPrice;
		erpContract.UserId = item.EmployeeNumber;
		erpContract.Fees1 = contract.Fees1;
		erpContract.Fees2 = contract.Fees2;
		erpContract.UpdateDate = DateTime.Now;
		erpContract.EmployeeNumber = item.EmployeeNumber;
		erpContract.CustomerNumber = item.Customer == null ? null : item.Customer;
		erpContract.CropYear = contract.CropYear;
		erpContract.PriceStatus = contract.Status.ToString();
		erpContract.InventoryLocation = item.DeliveryLocationName;
		erpContract.ContractNumber = contract.Number;
		erpContract.FreightCode = contract.FreightPrice != 0 ? "FOB" : "DELVD";
		erpContract.TheirContract = contract.TheirContract ?? "";
		erpContract.Comments = contract.Comments;
		erpContract.TenantId = TenantId;
		erpContract.CreationDate = contract.CreatedOn;
		erpContract.CustomFields = await GetContractMeta(contract);
	}

	public async Task<ErpResponse> GetErpContract(string number)
	{
		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
				erpResponse = ErpResponse.NewError(0, "There is no ERP connection");
			else
				erpResponse = await erp.GetErpContract(number);
		}
		catch (Exception ex)
		{
			erpResponse = ErpResponse.NewError(0, ex.ToString());
		}
		return erpResponse;
	}

	public async Task<ErpContractStatus> ProcessNewHedge(OrderFill fill, MarketTransaction transaction)
	{
		var status = new ErpContractStatus();
		status.IntegrationGuid = fill.InternalCode;
		status.TenantId = TenantId;

		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = fill.InternalCode,
			ErpAction = ErpAction.CreateHedge,
			ContractStatus = status
		};

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			fill.ErpStatus = erp == null ? ErpStatus.NoErp : ErpStatus.Pending;
			await _dbContext.SaveChangesAsync();

			status.ErpStatus = fill.ErpStatus;
			if (erp == null)
				return status;

			erpResponse = await erp.CreateHedge(fill, transaction);
			status.ContractNumber = erpResponse.ContractNumber;
			status.ErpStatus = erpResponse.ErpStatus;
			status.ErrorMessage = string.Join(';', erpResponse.ErrorList);
			erpEvent.RequestPayload = erpResponse.RequestPayload;
			erpEvent.ResponsePayload = erpResponse.ResponsePayload;
			erpEvent.ErpName = erp.Name;
		}
		catch (Exception ed)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErpStatus = ErpStatus.Fail;
			status.ErrorMessage = ed.Message;
		}

		SendChangeErpStatusEvent(erpEvent);
		return status;
	}

	public async Task<ErpContractStatus> ProcessMatchedHedge(OrderFill fill, FutureMatchDetail match)
	{
		var status = new ErpContractStatus()
		{
			IntegrationGuid = fill.InternalCode,
			TenantId = TenantId
		};

		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = fill.InternalCode,
			ErpAction = ErpAction.MatchHedge,
			ContractStatus = status
		};

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
				status.ErpStatus = ErpStatus.NoErp;
			else
			{
				erpResponse = await erp.MatchHedge(match);
				status.ContractNumber = erpResponse.ContractNumber;
				status.ErpStatus = erpResponse.ErpStatus;
				status.ErrorMessage = string.Join(';', erpResponse.ErrorList);
				erpEvent.RequestPayload = erpResponse.RequestPayload;
				erpEvent.ResponsePayload = erpResponse.ResponsePayload;
				erpEvent.ErpName = erp.Name;
			}
		}
		catch (Exception ex)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErpStatus = ErpStatus.Fail;
			status.ErrorMessage = ex.Message;
		}

		SendChangeErpStatusEvent(erpEvent);
		return status;
	}


	public async Task<ErpContractStatus> ProcessNtcConversion(Contract contract)
	{
		_logWorkflow.SetContract(contract.Id);
		await _logWorkflow.AddLog("ERP ProcessNtcConversion", "Process starts");
		var status = new ErpContractStatus();
		{
			status.IntegrationGuid = contract.InternalCode;
			status.TenantId = TenantId;
		}
		var erpEvent = new ErpStatusChangeEvent
		{
			InternalCode = contract.InternalCode,
			ErpAction = ErpAction.Conversion,
			ContractStatus = status
		};

		ErpResponse erpResponse = null;
		try
		{
			var erp = await GetErpIntegration();
			if (erp == null)
			{
				status.ErpStatus = ErpStatus.NoErp;
				await _logWorkflow.AddLog("ERP ProcessNtcConversion", "No ERP Status");
			}
			else
			{
				await _logWorkflow.AddLog("ERP ProcessNtcConversion", $"Process sends to {erp.Name}");
				var parentContract = await _contractRepository.GetById(contract.ParentId.Value);
				erpResponse = await erp.ConvertContract(contract, parentContract);
				status.ContractNumber = erpResponse.ContractNumber;
				status.ErpStatus = erpResponse.ErpStatus;
				status.ErrorMessage = string.Join(';', erpResponse.ErrorList);
				erpEvent.RequestPayload = erpResponse.RequestPayload;
				erpEvent.ResponsePayload = erpResponse.ResponsePayload;
				erpEvent.ErpName = erp.Name;
			}
		}
		catch (Exception ed)
		{
			status.ContractNumber = erpResponse?.ContractNumber;
			status.ErpStatus = ErpStatus.Fail;
			status.ErrorMessage = ed.Message;
		}

		SendChangeErpStatusEvent(erpEvent);
		await _logWorkflow.AddLog("ERP ProcessNtcConversion", $"Process ends: Status {status.ErpStatus}, Message {status.ErrorMessage}");
		return status;
	}
}
