using System.Runtime.Serialization;

namespace RJO.OrderService.Services.ErpIntegration.AgrisServices.Model;

public class UpdateContractInfoRequest : AgrisPayloadExpansion
{
	/// <summary>
	/// Gets or Sets ContractNumber
	/// </summary>
	[DataMember(Name = "ContractNumber")]
	public string ContractNumber { get; set; }

	/// <summary>
	/// Gets or Sets PurchaseSales
	/// </summary>
	[DataMember(Name = "PurchaseSales")]
	public string PurchaseSales { get; set; }

	/// <summary>
	/// Gets or Sets contractLocation
	/// </summary>
	[DataMember(Name = "contractLocation")]
	public string ContractLocation { get; set; }

	/// <summary>
	/// Gets or Sets ContractType
	/// </summary>
	[DataMember(Name = "ContractType")]
	public string ContractType { get; set; }

	/// <summary>
	/// Gets or Sets Commodity
	/// </summary>
	[DataMember(Name = "Commodity")]
	public string Commodity { get; set; }

	/// <summary>
	/// Gets or Sets NameID
	/// </summary>
	[DataMember(Name = "NameID")]
	public string NameID { get; set; }

	/// <summary>
	/// Gets or Sets ScheduledQuantity
	/// </summary>
	[DataMember(Name = "ScheduledQuantity")]
	public string ScheduledQuantity { get; set; }

	/// <summary>
	/// Gets or Sets IntegrationGuid
	/// </summary>
	[DataMember(Name = "IntegrationGuid")]
	public string IntegrationGuid { get; set; }

	/// <summary>
	/// Gets or Sets AgrisContractId
	/// </summary>
	[DataMember(Name = "AgrisContractId")]
	public string AgrisContractId { get; set; }

	/// <summary>
	/// Gets or Sets DeliveryPriceSchedules
	/// </summary>
	[DataMember(Name = "DeliveryPriceSchedules")]
	public List<DeliveryPricingSchedule> DeliveryPriceSchedules { get; set; }

	/// <summary>
	/// Gets or Sets UserId
	/// </summary>
	[DataMember(Name = "UserId")]
	public string UserId { get; set; }

	/// <summary>
	/// Gets or Sets Remarks
	/// </summary>
	[DataMember(Name = "Remarks")]
	public List<Remark> Remarks { get; set; }

	/// <summary>
	/// Gets or Sets DeliveryTerms
	/// </summary>
	[DataMember(Name = "DeliveryTerms")]
	public string DeliveryTerms { get; set; }
}
