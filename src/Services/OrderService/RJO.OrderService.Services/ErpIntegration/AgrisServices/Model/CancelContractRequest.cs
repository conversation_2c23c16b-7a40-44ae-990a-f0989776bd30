/* 
 * Agris API
 *
 * **[Postman Examples](https://documenter.getpostman.com/view/8851010/T17KeSEP?version=latest)**
 *
 * OpenAPI spec version: 1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System.Text;

namespace RJO.OrderService.Services.ErpIntegration.AgrisServices.Model;

/// <summary>
/// CancelContractRequest
/// </summary>
[DataContract]
public class CancelContractRequest : IEquatable<CancelContractRequest>, IValidatableObject
{
	/// <summary>
	/// Initializes a new instance of the <see cref="CancelContractRequest" /> class.
	/// </summary>
	/// <param name="integrationGuid">integrationGuid (required).</param>
	/// <param name="contractLocation">contractLocation (required).</param>
	/// <param name="contractNumber">contractNumber (required).</param>
	/// <param name="purchaseSales">purchaseSales (required).</param>
	/// <param name="deliveryPriceSchedules">deliveryPriceSchedules (required).</param>
	/// <param name="userId">userId (required).</param>
	public CancelContractRequest(string integrationGuid = default, string contractLocation = default, string contractNumber = default, string purchaseSales = default,
		List<DeliveryPriceScheduleRequestNumbers> deliveryPriceSchedules = default, string userId = default)
	{
		// to ensure "integrationGuid" is required (not null)
		if (integrationGuid == null)
		{
			throw new InvalidDataException("integrationGuid is a required property for CancelContractRequest and cannot be null");
		}

		IntegrationGuid = integrationGuid;
		// to ensure "contractLocation" is required (not null)
		if (contractLocation == null)
		{
			throw new InvalidDataException("contractLocation is a required property for CancelContractRequest and cannot be null");
		}

		ContractLocation = contractLocation;
		// to ensure "contractNumber" is required (not null)
		if (contractNumber == null)
		{
			throw new InvalidDataException("contractNumber is a required property for CancelContractRequest and cannot be null");
		}

		ContractNumber = contractNumber;
		// to ensure "purchaseSales" is required (not null)
		if (purchaseSales == null)
		{
			throw new InvalidDataException("purchaseSales is a required property for CancelContractRequest and cannot be null");
		}

		PurchaseSales = purchaseSales;
		// to ensure "deliveryPriceSchedules" is required (not null)
		if (deliveryPriceSchedules == null)
		{
			throw new InvalidDataException("deliveryPriceSchedules is a required property for CancelContractRequest and cannot be null");
		}

		DeliveryPriceSchedules = deliveryPriceSchedules;
		// to ensure "userId" is required (not null)
		if (userId == null)
		{
			throw new InvalidDataException("userId is a required property for CancelContractRequest and cannot be null");
		}

		UserId = userId;
	}

	/// <summary>
	/// Gets or Sets IntegrationGuid
	/// </summary>
	[DataMember(Name = "integrationGuid", EmitDefaultValue = false)]
	public string IntegrationGuid { get; set; }

	/// <summary>
	/// Gets or Sets ContractLocation
	/// </summary>
	[DataMember(Name = "contractLocation", EmitDefaultValue = false)]
	public string ContractLocation { get; set; }

	/// <summary>
	/// Gets or Sets ContractNumber
	/// </summary>
	[DataMember(Name = "contractNumber", EmitDefaultValue = false)]
	public string ContractNumber { get; set; }

	/// <summary>
	/// Gets or Sets PurchaseSales
	/// </summary>
	[DataMember(Name = "purchaseSales", EmitDefaultValue = false)]
	public string PurchaseSales { get; set; }

	/// <summary>
	/// Gets or Sets DeliveryPriceSchedules
	/// </summary>
	[DataMember(Name = "deliveryPriceSchedules", EmitDefaultValue = false)]
	public List<DeliveryPriceScheduleRequestNumbers> DeliveryPriceSchedules { get; set; }

	/// <summary>
	/// Gets or Sets UserId
	/// </summary>
	[DataMember(Name = "userId", EmitDefaultValue = false)]
	public string UserId { get; set; }

	/// <summary>
	/// Returns the string presentation of the object
	/// </summary>
	/// <returns>String presentation of the object</returns>
	public override string ToString()
	{
		var sb = new StringBuilder();
		sb.Append("class CancelContractRequest {\n");
		sb.Append("  IntegrationGuid: ").Append(IntegrationGuid).Append("\n");
		sb.Append("  ContractLocation: ").Append(ContractLocation).Append("\n");
		sb.Append("  ContractNumber: ").Append(ContractNumber).Append("\n");
		sb.Append("  PurchaseSales: ").Append(PurchaseSales).Append("\n");
		sb.Append("  DeliveryPriceSchedules: ").Append(DeliveryPriceSchedules).Append("\n");
		sb.Append("  UserId: ").Append(UserId).Append("\n");
		sb.Append("}\n");
		return sb.ToString();
	}

	/// <summary>
	/// Returns the JSON string presentation of the object
	/// </summary>
	/// <returns>JSON string presentation of the object</returns>
	public virtual string ToJson() => JsonConvert.SerializeObject(this, Formatting.Indented);

	/// <summary>
	/// Returns true if objects are equal
	/// </summary>
	/// <param name="input">Object to be compared</param>
	/// <returns>Boolean</returns>
	public override bool Equals(object input) => Equals(input as CancelContractRequest);

	/// <summary>
	/// Returns true if CancelContractRequest instances are equal
	/// </summary>
	/// <param name="input">Instance of CancelContractRequest to be compared</param>
	/// <returns>Boolean</returns>
	public bool Equals(CancelContractRequest input)
	{
		if (input == null)
			return false;

		return
			(
				IntegrationGuid == input.IntegrationGuid ||
				(IntegrationGuid != null &&
				 IntegrationGuid.Equals(input.IntegrationGuid, StringComparison.Ordinal))
			) &&
			(
				ContractLocation == input.ContractLocation ||
				(ContractLocation != null &&
				 ContractLocation.Equals(input.ContractLocation, StringComparison.Ordinal))
			) &&
			(
				ContractNumber == input.ContractNumber ||
				(ContractNumber != null &&
				 ContractNumber.Equals(input.ContractNumber, StringComparison.Ordinal))
			) &&
			(
				PurchaseSales == input.PurchaseSales ||
				(PurchaseSales != null &&
				 PurchaseSales.Equals(input.PurchaseSales, StringComparison.Ordinal))
			) &&
			(
				DeliveryPriceSchedules == input.DeliveryPriceSchedules ||
				(DeliveryPriceSchedules != null &&
				 input.DeliveryPriceSchedules != null &&
				 DeliveryPriceSchedules.SequenceEqual(input.DeliveryPriceSchedules))
			) &&
			(
				UserId == input.UserId ||
				(UserId != null &&
				 UserId.Equals(input.UserId, StringComparison.Ordinal))
			);
	}

	/// <summary>
	/// Gets the hash code
	/// </summary>
	/// <returns>Hash code</returns>
	public override int GetHashCode()
	{
		unchecked // Overflow is fine, just wrap
		{
			var hashCode = 41;
			if (IntegrationGuid != null)
				hashCode = (hashCode * 59) + IntegrationGuid.GetHashCode();
			if (ContractLocation != null)
				hashCode = (hashCode * 59) + ContractLocation.GetHashCode();
			if (ContractNumber != null)
				hashCode = (hashCode * 59) + ContractNumber.GetHashCode();
			if (PurchaseSales != null)
				hashCode = (hashCode * 59) + PurchaseSales.GetHashCode();
			if (DeliveryPriceSchedules != null)
				hashCode = (hashCode * 59) + DeliveryPriceSchedules.GetHashCode();
			if (UserId != null)
				hashCode = (hashCode * 59) + UserId.GetHashCode();
			return hashCode;
		}
	}

	/// <summary>
	/// To validate all properties of the instance
	/// </summary>
	/// <param name="validationContext">Validation context</param>
	/// <returns>Validation Result</returns>
	IEnumerable<ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
	{
		yield break;
	}
}
