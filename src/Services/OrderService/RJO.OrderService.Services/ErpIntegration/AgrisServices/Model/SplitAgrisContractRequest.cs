namespace RJO.OrderService.Services.ErpIntegration.AgrisServices.Model;

public class SplitAgrisContractRequest
{
	public string DeliveryPriceSchedules { get; set; }
	public string ContractNumber { get; set; }
	public string IntegrationGuid { get; set; }
	public string PurchaseSales { get; set; }
	public string Location { get; set; }
	public string OrderTransactionId { get; set; }
	public string ScheduleNumber { get; set; }
	public string SystemAcceptedDateTime { get; set; }
	public string ErrorMessage { get; set; }
	public string Status { get; set; }
	public string DetailedStatus { get; set; }
	public int ErrorCode { get; set; }
	public string RequestIndentifier { get; set; }
	public RequestMeta RequestMeta { get; set; }
	public int RequestType { get; set; }
	public string AdapterPayload { get; set; }
}
