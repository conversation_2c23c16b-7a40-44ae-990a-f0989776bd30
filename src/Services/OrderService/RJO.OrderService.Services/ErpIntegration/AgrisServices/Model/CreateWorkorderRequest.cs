/* 
 * Agris API
 *
 * **[Postman Examples](https://documenter.getpostman.com/view/8851010/T17KeSEP?version=latest)**
 *
 * OpenAPI spec version: 1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System.Text;

namespace RJO.OrderService.Services.ErpIntegration.AgrisServices.Model;

/// <summary>
/// CreateWorkorderRequest
/// </summary>
[DataContract]
public class CreateWorkorderRequest : IEquatable<CreateWorkorderRequest>, IValidatableObject
{
	/// <summary>
	/// Initializes a new instance of the <see cref="CreateWorkorderRequest" /> class.
	/// </summary>
	/// <param name="formulation">formulation (required).</param>
	/// <param name="agrisItems">agrisItems (required).</param>
	public CreateWorkorderRequest(AllOfCreateWorkorderRequestFormulation formulation = default, List<AgrisItem> agrisItems = default)
	{
		// to ensure "formulation" is required (not null)
		if (formulation == null)
		{
			throw new InvalidDataException("formulation is a required property for CreateWorkorderRequest and cannot be null");
		}

		Formulation = formulation;
		// to ensure "agrisItems" is required (not null)
		if (agrisItems == null)
		{
			throw new InvalidDataException("agrisItems is a required property for CreateWorkorderRequest and cannot be null");
		}

		AgrisItems = agrisItems;
	}

	/// <summary>
	/// Gets or Sets Formulation
	/// </summary>
	[DataMember(Name = "formulation", EmitDefaultValue = false)]
	public AllOfCreateWorkorderRequestFormulation Formulation { get; set; }

	/// <summary>
	/// Gets or Sets AgrisItems
	/// </summary>
	[DataMember(Name = "agrisItems", EmitDefaultValue = false)]
	public List<AgrisItem> AgrisItems { get; set; }

	/// <summary>
	/// Returns the string presentation of the object
	/// </summary>
	/// <returns>String presentation of the object</returns>
	public override string ToString()
	{
		var sb = new StringBuilder();
		sb.Append("class CreateWorkorderRequest {\n");
		sb.Append("  Formulation: ").Append(Formulation).Append("\n");
		sb.Append("  AgrisItems: ").Append(AgrisItems).Append("\n");
		sb.Append("}\n");
		return sb.ToString();
	}

	/// <summary>
	/// Returns the JSON string presentation of the object
	/// </summary>
	/// <returns>JSON string presentation of the object</returns>
	public virtual string ToJson() => JsonConvert.SerializeObject(this, Formatting.Indented);

	/// <summary>
	/// Returns true if objects are equal
	/// </summary>
	/// <param name="input">Object to be compared</param>
	/// <returns>Boolean</returns>
	public override bool Equals(object input) => Equals(input as CreateWorkorderRequest);

	/// <summary>
	/// Returns true if CreateWorkorderRequest instances are equal
	/// </summary>
	/// <param name="input">Instance of CreateWorkorderRequest to be compared</param>
	/// <returns>Boolean</returns>
	public bool Equals(CreateWorkorderRequest input)
	{
		if (input == null)
			return false;

		return
			(
				Formulation == input.Formulation ||
				(Formulation != null &&
				 Formulation.Equals(input.Formulation))
			) &&
			(
				AgrisItems == input.AgrisItems ||
				(AgrisItems != null &&
				 input.AgrisItems != null &&
				 AgrisItems.SequenceEqual(input.AgrisItems))
			);
	}

	/// <summary>
	/// Gets the hash code
	/// </summary>
	/// <returns>Hash code</returns>
	public override int GetHashCode()
	{
		unchecked // Overflow is fine, just wrap
		{
			var hashCode = 41;
			if (Formulation != null)
				hashCode = (hashCode * 59) + Formulation.GetHashCode();
			if (AgrisItems != null)
				hashCode = (hashCode * 59) + AgrisItems.GetHashCode();
			return hashCode;
		}
	}

	/// <summary>
	/// To validate all properties of the instance
	/// </summary>
	/// <param name="validationContext">Validation context</param>
	/// <returns>Validation Result</returns>
	IEnumerable<ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
	{
		yield break;
	}
}
