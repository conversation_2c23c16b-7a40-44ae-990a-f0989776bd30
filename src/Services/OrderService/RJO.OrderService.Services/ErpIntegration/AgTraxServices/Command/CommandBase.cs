using Microsoft.Extensions.DependencyInjection;
using System.Collections.Specialized;
using System.Net;
using System.Text;
using System.Xml;
using System.Xml.Serialization;

namespace RJO.OrderService.Services.ErpIntegration.AgTraxServices.Command;

public class CommandBase
{
	protected readonly IServiceProvider _currentServiceProvider;
	
	protected CommandBase(CommandEnvironment environment, Type commandType, Type responseType, IServiceProvider currentServiceProvider)
	{
		CommandEnvironment = environment;
		CommandType = commandType;
		ResponseType = responseType;
		_currentServiceProvider = currentServiceProvider;
	}

	public CommandEnvironment CommandEnvironment { get; set; }

	public virtual string Url => CommandEnvironment.RootUrl;

	public virtual NameValueCollection GetNamedValues() => null;

	public string CommandXML => SerializeCommand(Command);

	protected Type CommandType { get; private set; }
	protected Type ResponseType { get; private set; }
	protected object Command { get; set; }
	protected object Response { get; set; }

	string _responseXML;

	public string ResponseXML => _responseXML;

	string SerializeCommand(object obj)
	{
		if (obj == null)
			return null;

		using (var memoryStream = new MemoryStream())
		{
			var serializer = new XmlSerializer(CommandType);
			serializer.Serialize(memoryStream, obj);

			var xml = new XmlDocument();
			memoryStream.Position = 0;
			xml.Load(memoryStream);

			return xml.InnerXml;
		}
	}

	object DeserializeResponse(byte[] bytes)
	{
		if (bytes == null)
			return null;

		var ser = new XmlSerializer(ResponseType);

		using var memoryStream = new MemoryStream(bytes);
		using var xmlReader = XmlReader.Create(memoryStream);
		Response = ser.Deserialize(xmlReader);
		return Response;
	}

	object DeserializeResponse(string response)
	{
		if (response == null)
			return null;
		
		var ser = new XmlSerializer(ResponseType);
		Response = ser.Deserialize(response);
		return Response;
	}

	[Obsolete("Method is deprecated, please use ExecuteAsync instead.")]
	public bool Execute()
	{
		using (var client = new WebClient())
		{
			var values = GetNamedValues();
			var rsp = client.UploadValues(Url, values);

			if (rsp != null)
				_responseXML = Encoding.Default.GetString(rsp);

			DeserializeResponse(rsp);
		}

		return Response != null;
	}

	public async Task<bool> ExecuteAsync()
	{
		var httpFactory = _currentServiceProvider.GetService<IHttpClientFactory>();
		var httpClient = httpFactory.CreateClient();
		var httpResponse = await httpClient.PostAsync(Url, GetRequestPayload());
		_responseXML = await httpResponse.Content.ReadAsStringAsync();
		if (!string.IsNullOrEmpty(_responseXML))
			DeserializeResponse(_responseXML);

		return Response != null;
	}

	StringContent GetRequestPayload()
	{
		var values = GetNamedValues();

		var encodedItems = new List<string>();
		foreach (string key in values)
		{
			foreach (var value in values.GetValues(key)!)
			{
				encodedItems.Add($"{WebUtility.UrlEncode(key)}={WebUtility.UrlEncode(value)}");
			}
		}
		return new(string.Join("&", encodedItems), Encoding.UTF8, "application/x-www-form-urlencoded");
	}

	protected void CommandFromXML(string xmlCommand)
	{
		var ser = new XmlSerializer(CommandType);
		Command = ser.Deserialize(xmlCommand);
	}
}
