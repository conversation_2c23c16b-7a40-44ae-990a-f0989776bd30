using System.Text;
using System.Reflection;
using System.Globalization;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Domain.Metadata;
using RJO.OrderService.Services.Helper;
using RJO.OrderService.Services.Services.OrderProcess;
using RJO.OrderService.Services.ErpIntegration.AgVantageServices.Model;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Services.ErpIntegration.AgVantageServices.Api;

public class AgvantageErpProxy 
{
	const string HrvystCancelComment = "Canceled in Hrvyst";
	const string NotSupportedContractType = "Contract type is not supported";
	const string NoSaleContract = "Sale contract not supported by AgVantage";
	const string NoCancellation = "Contract cancellation not supported by AgVantage";
	const string NoContractUpdate = "Base contract update not supported by AgVantage";
	const string AgvantageFees1 = "Agvantage.Fees1";
	const string AgvantageFees2 = "Agvantage.Fees2";
	const string AgvantageFreight = "Agvantage.Freight";

	AgvantageSetting _agvantageSetting;
	readonly ErpIntegrationAgent _integrationAgent;
	readonly AppDbContext _dbContext;
	readonly AgvantageClient _client;

	public AgvantageErpProxy(ErpIntegrationAgent erpIntegrationAgent)
	{
		_integrationAgent = erpIntegrationAgent;
		_dbContext = erpIntegrationAgent.DbContext;
		_client = new(erpIntegrationAgent.ServiceProvider);
	}

	#region supporting methods
	static bool TryParseContractNumber(string contractNumber, out string number, out int seq)
	{
		number = null;
		seq = 0;

		var parts = contractNumber?.Split('.');
		if (parts?.Length == 2 && int.TryParse(parts[1], out seq))
		{
			number = parts[0];
			return true;
		}

		return false;
	}

	static int GetType(Contract contract) =>
		// type - Required and must be a 11, 12, 13, or 14 for Fixed Price, Basis, Extended Time to Decide(ETD) and Minimum Price contracts respectively.
		contract.ContractTypeId switch
		{
			var id when id == ContractTypeDictionary.FlatPrice => 11,
			var id when id == ContractTypeDictionary.Basis => 12,
			var id when id == ContractTypeDictionary.HTA => 13,
			_ => 0
		};

	public async Task<string> GetAgvantageContractId(Guid contractId)
	{
		var agvantageContract = await _dbContext.Set<AgvantageContract>().AsNoTracking()
			.FirstOrDefaultAsync(x => x.ContractId == contractId);
		AssertionConcern.ArgumentIsNotNull(agvantageContract, "Missing Agvantage contract number");
		return agvantageContract.AgvantageId;
	}

	async Task UpsertIdAgvatangeContract(Guid contractId, string agvantageId, CancellationToken cancellationToken)
	{
		var agvantageContract = await _dbContext.AgvantageContract.FirstOrDefaultAsync(x => x.ContractId == contractId, cancellationToken);
		if (agvantageContract == null)
		{
			await _dbContext.AgvantageContract.AddAsync(new AgvantageContract
			{
				ContractId = contractId,
				AgvantageId = agvantageId
			}, cancellationToken);
			await _dbContext.SaveChangesAsync(cancellationToken);
		}
		else if (agvantageContract.AgvantageId != agvantageId)
		{
			agvantageContract.AgvantageId = agvantageId;
			await _dbContext.SaveChangesAsync(cancellationToken);
		}
	}

	public async Task VerifyContract(Contract contract, CancellationToken cancellationToken = default)
	{
		if (_agvantageSetting == null)
		{
			_agvantageSetting = await _dbContext.Set<AgvantageSetting>().AsNoTracking().FirstOrDefaultAsync(cancellationToken);
			AssertionConcern.ArgumentIsNotNull(_agvantageSetting, $"No ERP connection setting found for contract {contract.Id}");
		}
	}

	async Task AssignCustomFields(Contract contract, AgvantageNewContractRequest request)
	{
		var qeuryCustomProperties = from m in _dbContext.Set<ContractMetadata>()
									join mc in _dbContext.Set<OrderMetadataConfiguration>() on m.FieldId.Value equals mc.Id
									join i in _dbContext.Set<OrderMetadataItem>()
										on new { mc.Id, m.Value } equals new { Id = i.ConfigurationId, i.Value } into mvalue
									from i in mvalue.DefaultIfEmpty()
									where m.ContractId == contract.Id && mc.ErpField != null
									select new
									{
										Key = mc.ErpField,
										ErpValue = i.ErpValue != null ? i.ErpValue : m.Value
									};
		var customProperties = await qeuryCustomProperties.ToDictionaryAsync(x => x.Key, x => x.ErpValue);
		if (customProperties.Count == 0)
			return;

		var type = typeof(AgvantageNewContractRequest);
		foreach (var propertyPair in customProperties)
		{
			if (propertyPair.Value != null)
				UpdateProperty(request, type, propertyPair);
		}
	}

	static void UpdateProperty(AgvantageNewContractRequest request, Type type, KeyValuePair<string, string> propertyPair)
	{
		try
		{
			var property = type.GetProperty(propertyPair.Key, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
			if (property == null)
				request.CustomProperties[propertyPair.Key] = JsonDocument.Parse(JsonSerializer.Serialize(propertyPair.Value)).RootElement;
			else
			{
				var targetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;
				object value = targetType == typeof(int) && propertyPair.Value.Contains('.', StringComparison.Ordinal)
					? Convert.ChangeType(Convert.ToDouble(propertyPair.Value), targetType)
					: Convert.ChangeType(propertyPair.Value, targetType, CultureInfo.InvariantCulture);
				property.SetValue(request, value);
			}
		}
		catch(Exception ex)
		{
			throw new BusinessException($"Failed to set CustomField {propertyPair.Key} with {propertyPair.Value}: {ex.Message}");
		}
	}
	#endregion

	#region build request
	async Task AddAdjustment(AgvantageNewContractRequest request, string name, decimal fee, CancellationToken cancellationToken)
	{
		if (fee == 0)
			return;

		var adjustmentMapping = await _dbContext.ApplicationSettings.AsNoTracking()
			.FirstOrDefaultAsync(x => x.TenantId == _integrationAgent.TenantId && x.Type == ESettingsType.Tenant && x.Name == name && x.IsActive, cancellationToken);
		if (adjustmentMapping == null)
			return;

		var adjustment = new NewContractAdjustment
		{
			Id = adjustmentMapping.Value,
			Rate = fee
		};
		request.Adjustments.Add(adjustment);
	}

	async Task<AgvantageNewContractRequest> BuildContractRequest(Contract contract, CancellationToken cancellationToken = default)
	{
		var customer = await _dbContext.Customers
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == contract.CustomerId, cancellationToken);
		AssertionConcern.ArgumentIsNotNull(customer, "Invalid Customer");
		var location = await _dbContext.Locations
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == contract.LocationId, cancellationToken);
		AssertionConcern.ArgumentIsNotNull(location, "Invalid Location");

		var employee = await _dbContext.Employees
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == contract.EmployeeId, cancellationToken);
		AssertionConcern.ArgumentIsTrue(int.TryParse(employee.Number.Trim(), out var employeeNumber), "Invalid Employee");

		var commodity = await _dbContext.Commodities
			.AsNoTracking()
			.Include("Product")
			.FirstOrDefaultAsync(x => x.Id == contract.CommodityId, cancellationToken);
		AssertionConcern.ArgumentIsNotNull(commodity, "Invalid Commodity");

		var contractType = GetType(contract);
		AssertionConcern.ArgumentIsTrue(contractType != 0, "Invalid AgVantage contract type");

		// FuturesMonth format: H23
		var futureMonth = FutureMonthsHelper.GetMonthNumberFromMonthCode(contract.FuturesMonth[0]);
		AssertionConcern.ArgumentIsTrue(int.TryParse(contract.FuturesMonth[1..].Trim(), out var futureYear), "Invalid Future Year");
		AssertionConcern.ArgumentIsTrue(int.TryParse(customer.Number.Trim(), out var customerNumber), "Invalid Customer Number");
		AssertionConcern.ArgumentIsTrue(int.TryParse(location.Number.Trim(), out var locationNumber), "Invalid Location Number");

		var commodityMapping = await _dbContext.ErpIdMapping.AsNoTracking()
			.FirstOrDefaultAsync(x => x.ErpName == ErpConstants.Agvantage && x.MappingType == ErpIdMappingType.Commodity && x.SourceId == contract.CommodityId, cancellationToken);
		var locationMapping = await _dbContext.ErpIdMapping.AsNoTracking()
			.FirstOrDefaultAsync(x => x.ErpName == ErpConstants.Agvantage && x.MappingType == ErpIdMappingType.Location && x.SourceId == contract.DeliveryLocationId, cancellationToken);
		AssertionConcern.ArgumentIsNotTrue(string.IsNullOrEmpty(commodityMapping?.DestinationValue), "Invalid Grade in ErpIdMapping table");
		AssertionConcern.ArgumentIsTrue(int.TryParse(commodityMapping.DestinationValue.Trim(), out var grade), "Invalid Grade value");
		AssertionConcern.ArgumentIsNotTrue(string.IsNullOrEmpty(locationMapping?.DestinationValue), "Invalid DeliveryBasis in ErpIdMapping table");

		var cashPrice = contract.ContractTypeId == ContractTypeDictionary.FlatPrice	? (contract.NetBasis ?? 0m) + (contract.FuturesPrice ?? 0m) : 0m;
		string expirationDate = contractType == 11 ? null : (contract.Expiration ?? DateTime.Now).ToString("MM/dd/yyyy");
		var deliveryTerms = "D";

		var request = new AgvantageNewContractRequest
		{
			Type = contractType,
			BuyerId = employeeNumber,
			CropId = commodity.Number,
			ControlDate = contract.CreatedOn.ToString("MM/dd/yyyy"),
			CustomerId = customerNumber,
			DeliveryStartDate = contract.DeliveryStartDate.ToString("MM/dd/yyyy"),
			DeliveryEndDate = contract.DeliveryEndDate.ToString("MM/dd/yyyy"),
			LocationId = locationNumber,
			CashPrice = cashPrice,
			BasisAmount = contract.NetBasis ?? 0m,
			FuturesPrice = contract.FuturesPrice ?? 0m,
			FuturesMonth = futureMonth,
			FuturesYear = 2000 + futureYear, 
			FinalPricingDate = expirationDate,
			ContractedBushels = contract.GrossRemainingBalance,
			Grade = grade,
			DeliveryBasis = locationMapping.DestinationValue, 
			DeliveryTerms = deliveryTerms
		};

		await AddAdjustment(request, AgvantageFees1, contract.Fees1, cancellationToken);
		await AddAdjustment(request, AgvantageFees2, contract.Fees2, cancellationToken);
		await AddAdjustment(request, AgvantageFreight, contract.FreightPrice, cancellationToken);

		await AssignCustomFields(contract, request);
		return request;
	}

	async Task<AgvantagePricingContractRequest> BuildPricingRequest(Contract contract, decimal quantity, string comment, CancellationToken cancellationToken = default)
	{
		var employee = await _dbContext.Employees
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == contract.EmployeeId, cancellationToken);
		AssertionConcern.ArgumentIsTrue(int.TryParse(employee.Number.Trim(), out var employeeNumber), "Invalid Employee");

		var futureMonth = FutureMonthsHelper.GetMonthNumberFromMonthCode(contract.FuturesMonth[0]);
		AssertionConcern.ArgumentIsTrue(int.TryParse(contract.FuturesMonth[1..].Trim(), out var futureYear), "Invalid Future Year");
		var finalPrice = (contract.NetBasis ?? 0m) + (contract.FuturesPrice ?? 0m);
		var request = new AgvantagePricingContractRequest
		{
			BuyerID = employeeNumber,
			Date = contract.CreatedOn.ToString("MM/dd/yyyy"),
			FinalPrice = finalPrice,
			BasisAmount = contract.NetBasis ?? 0m,
			FuturesPrice = contract.FuturesPrice ?? 0m,
			FuturesYear = 2000 + futureYear,
			FuturesMonth = futureMonth,
			PricedBushels = quantity
		};

		if (!string.IsNullOrEmpty(contract.Comments))
			request.Comments.Add(contract.Comments);
		if (!string.IsNullOrEmpty(comment))
			request.Comments.Add(comment);

		return request;
	}

	static AgvantageDeletePricedContractRequest BuildDelPricedRequest(int seq) => new() { Seq = seq };
	#endregion

	#region parse response
	async Task<ErpResponse> ParseNewContractResponse(Contract contract, HttpResponseMessage response, string requestBody, string responseBody, CancellationToken cancellationToken)
	{
		ErpResponse erpResponse;
		if (response.IsSuccessStatusCode)
		{
			try
			{
				var agvantageSuccess = JsonSerializer.Deserialize<AgvantageNewSuccessResponse>(responseBody);	
				var controlNumber = agvantageSuccess.Id[1..10];
				await UpsertIdAgvatangeContract(contract.Id, agvantageSuccess.Id, cancellationToken);
				erpResponse = ErpResponse.NewOK(controlNumber.TrimStart('0'));
			}
			catch (Exception ex)
			{
				erpResponse = ErpResponse.NewError((int)response.StatusCode, $"AgVantage payload format error: {ex.Message}");
			}
		}
		else
		{
			string errorMsg = $"{response.StatusCode}: ";
			try
			{
				var agvantageError = JsonSerializer.Deserialize<AgvantageErrorResponse>(responseBody);
				errorMsg += string.Join(",", agvantageError.Errors) + " | "; 
				errorMsg += string.Join(", ", agvantageError.Legacy.Select(a => $"{a.Property}: {a.Message}"));
				errorMsg = Regex.Replace(errorMsg, @"\s+", " ");
			}
			catch (Exception)
			{
				errorMsg += responseBody;
			}
			erpResponse = ErpResponse.NewError((int)response.StatusCode, errorMsg);
		}

		erpResponse.RequestPayload = requestBody;
		erpResponse.ResponsePayload = responseBody;
		return erpResponse;
	}

	async Task<ErpResponse> ParsePricingResponse(Contract contract, HttpResponseMessage response, string requestBody, string responseBody, CancellationToken cancellationToken) =>
		await ParsePriceResponse(contract, response, requestBody, responseBody, async () =>
		{
			var agvantageSuccess = JsonSerializer.Deserialize<AgvantagePricingSuccessResponse>(responseBody);
			var priceItem = agvantageSuccess.Prices.OrderByDescending(p => p.Seq).FirstOrDefault();
			if (priceItem == null)
				return ErpResponse.NewError((int)response.StatusCode, $"There is no price item found in AgVantage response");

			await UpsertIdAgvatangeContract(contract.Id, agvantageSuccess.Id, cancellationToken);
			return ErpResponse.NewOK($"{agvantageSuccess.ControlNumber}.{priceItem.Seq}");
		});

	static async Task<ErpResponse> ParseDelPricedResponse(Contract contract, HttpResponseMessage response, string requestBody, string responseBody, int seq) =>
		await ParsePriceResponse(contract, response, requestBody, responseBody, async () =>
		{
			var agvantageSuccess = JsonSerializer.Deserialize<AgvantageDeletePricedContractResponse>(responseBody);
			var priceItem = agvantageSuccess.Prices.FirstOrDefault(x => x.Seq == seq);
			return priceItem != null
				 ? ErpResponse.NewError((int)response.StatusCode, $"The price item isn't deleted in AgVantage response")
				 : ErpResponse.NewOK($"{agvantageSuccess.ControlNumber}.{seq}");
		});

	static async Task<ErpResponse> ParsePriceResponse(Contract contract, HttpResponseMessage response, string requestBody, string responseBody
		, Func<Task<ErpResponse>> parseSuccessResponse)
	{
		ErpResponse erpResponse;
		if (response.IsSuccessStatusCode)
		{
			try
			{
				erpResponse = await parseSuccessResponse();
			}
			catch (Exception ex)
			{
				erpResponse = ErpResponse.NewError((int)response.StatusCode, $"AgVantage payload format error: {ex.Message}");
			}
		}
		else
		{
			string errorMsg = $"{response.StatusCode}: ";
			try
			{
				var agvantageError = JsonSerializer.Deserialize<AgvantagePricingErrorResponse>(responseBody);
				if (!string.IsNullOrEmpty(agvantageError.Error))
					errorMsg += agvantageError.Error;
				else
				{
					var agvantageError2 = JsonSerializer.Deserialize<AgvantageErrorResponse>(responseBody);
					errorMsg += string.Join(",", agvantageError2.Errors) + " | ";
					errorMsg += string.Join(", ", agvantageError2.Legacy.Select(a => $"{a.Property}: {a.Message}"));
					errorMsg = Regex.Replace(errorMsg, @"\s+", " ");
				}
			}
			catch (Exception)
			{
				errorMsg += responseBody;
			}
			erpResponse = ErpResponse.NewError((int)response.StatusCode, errorMsg);
		}

		erpResponse.RequestPayload = requestBody;
		erpResponse.ResponsePayload = responseBody;
		return erpResponse;
	}
	#endregion

	#region Agvantage interface
	public async Task<ErpResponse> CreateContract(Contract contract, CancellationToken cancellationToken = default)
	{
		if (contract.IsSell)
			return ErpResponse.NewNone(NoSaleContract);
		if (contract.ContractTypeId != ContractTypeDictionary.FlatPrice && contract.ContractTypeId != ContractTypeDictionary.Basis && contract.ContractTypeId != ContractTypeDictionary.HTA)
			return ErpResponse.NewNone(NotSupportedContractType);
		await VerifyContract(contract, cancellationToken);

		var response = await _client.Send(_agvantageSetting, async httpClient =>
		{
			var requestContent = await BuildContractRequest(contract, cancellationToken);
			var requestBody = JsonSerializer.Serialize(requestContent, requestContent.GetType(), ErpJsonSerializerOptions.DefaultAgvantageOptions);
			using StringContent jsonContent = new(requestBody, Encoding.UTF8, "application/json");

			var httpResponse = await httpClient.PostAsync(new Uri($"{_agvantageSetting.BaseAddress}/graincontracts"), jsonContent, cancellationToken);
			var responseBody = await httpResponse.Content.ReadAsStringAsync(cancellationToken);
			var erpResponse = await ParseNewContractResponse(contract, httpResponse, requestBody, responseBody, cancellationToken);
			return erpResponse;
		});
		return response;
	}

	public async Task<ErpResponse> PriceContract(Contract contract, string agvantageId, decimal? quantity = null, string comment = null, CancellationToken cancellationToken = default)
	{
		if (contract.IsSell)
			return ErpResponse.NewNone(NoSaleContract);
		await VerifyContract(contract, cancellationToken);

		var response = await _client.Send(_agvantageSetting, async (httpClient) =>
		{
			var uriString = $"{_agvantageSetting.BaseAddress}/graincontractprices/{agvantageId}";
			var requestContent = await BuildPricingRequest(contract, quantity ?? contract.GrossRemainingBalance, comment, cancellationToken);
			var requestBody = JsonSerializer.Serialize(requestContent, requestContent.GetType(), ErpJsonSerializerOptions.DefaultAgvantageOptions);
			using StringContent jsonContent = new(requestBody, Encoding.UTF8, "application/json");
			var httpResponse = await httpClient.PostAsync(new Uri(uriString), jsonContent, cancellationToken);
			var responseBody = await httpResponse.Content.ReadAsStringAsync(cancellationToken);
			var erpResponse = await ParsePricingResponse(contract, httpResponse, requestBody, responseBody, cancellationToken);
			return erpResponse;
		});
		return response;
	}

	public async Task<ErpResponse> DeletePricedContract(Contract contract, string number, int seq, CancellationToken cancellationToken = default)
	{
		if (contract.IsSell)
			return ErpResponse.NewNone(NoSaleContract);
		await VerifyContract(contract, cancellationToken);

		var response = await _client.Send(_agvantageSetting, async (httpClient) =>
		{
			var uriString = $"{_agvantageSetting.BaseAddress}/graincontractprices/{number}";
			var requestContent = BuildDelPricedRequest(seq);
			var requestBody = JsonSerializer.Serialize(requestContent, requestContent.GetType(), ErpJsonSerializerOptions.DefaultAgvantageOptions);
			using StringContent jsonContent = new(requestBody, Encoding.UTF8, "application/json");
			var request = new HttpRequestMessage
			{
				Method = HttpMethod.Delete,
				RequestUri = new Uri(uriString),
				Content = jsonContent
			};
			var httpResponse = await httpClient.SendAsync(request, cancellationToken);
			var responseBody = await httpResponse.Content.ReadAsStringAsync(cancellationToken);
			var erpResponse = await ParseDelPricedResponse(contract, httpResponse, requestBody, responseBody, seq);
			return erpResponse;
		});

		return response;
	}

	public async Task<ErpResponse> UpdateContract(Contract contract, CancellationToken cancellationToken = default)
	{
		if (contract.IsSell)
			return ErpResponse.NewNone(NoSaleContract);
		await VerifyContract(contract, cancellationToken);

		if (contract.ContractTypeId == ContractTypeDictionary.FlatPrice || !contract.IsOnStatus(EContractState.Priced))
			return ErpResponse.NewNone(NoContractUpdate);

		return await UpdatePricedContract(contract, cancellationToken: cancellationToken);
	}

	public async Task<ErpResponse> AdjustContract(Contract contract, decimal deletedQuantity, bool updateParent, CancellationToken cancellationToken = default)
	{
		if (contract.IsSell)
			return ErpResponse.NewNone(NoSaleContract);
		if (contract.Parent == null)
			return ErpResponse.NewNone(NoCancellation);
		await VerifyContract(contract, cancellationToken);

		AssertionConcern.ArgumentIsTrue(
			TryParseContractNumber(contract.Number, out var controlNumber, out var seq),
			$"Invalid AgVantage priced contract number: {contract?.Number ?? string.Empty}");
		var agvantageId = await GetAgvantageContractId(contract.Id);
		var erpResponse = updateParent
			? await DeletePricedContract(contract, agvantageId, seq, cancellationToken)
			: ErpResponse.NewNone(NoCancellation);
		return erpResponse;
	}

	async Task<ErpResponse> UpdatePricedContract(Contract contract, CancellationToken cancellationToken = default)
	{
		AssertionConcern.ArgumentIsTrue(
			TryParseContractNumber(contract.Number, out var controlNumber, out var seq),
			$"Invalid AgVantage priced contract number: {contract?.Number ?? string.Empty}");
		var agvantageId = await GetAgvantageContractId(contract.Id);
		var erpResponse = await DeletePricedContract(contract, agvantageId, seq, cancellationToken);
		if (!erpResponse.Success)
			return erpResponse;

		var erpLog = ErpLog.ErpSuccess(contract.Id, Guid.Empty, ErpAction.Update, erpResponse.RequestPayload, erpResponse.ResponsePayload, erpResponse.ContractNumber);
		await _dbContext.ErpLog.AddAsync(erpLog, cancellationToken);
		await _dbContext.SaveChangesAsync(cancellationToken);

		return await PriceContract(contract, agvantageId, cancellationToken: cancellationToken);
	}

	// TODO: Currently not in use; will be removed if determined to be unsupported.
	async Task<ErpResponse> CancelPricedContract(Contract contract, string agvantageId, int seq, decimal deletedQuantity, CancellationToken cancellationToken = default)
	{
		var erpResponse = await DeletePricedContract(contract, agvantageId, seq, cancellationToken);
		if (!erpResponse.Success)
			return erpResponse;
		var erpLog = ErpLog.ErpSuccess(contract.Id, Guid.Empty, ErpAction.Cancel, erpResponse.RequestPayload, erpResponse.ResponsePayload, erpResponse.ContractNumber);
		await _dbContext.ErpLog.AddAsync(erpLog, cancellationToken);

		// Create the original contact with special comment
		erpResponse = await PriceContract(contract, agvantageId, contract.GrossRemainingBalance - deletedQuantity, HrvystCancelComment, cancellationToken);
		if (!erpResponse.Success || contract.GrossRemainingBalance == 0)
		{
			await _dbContext.SaveChangesAsync(cancellationToken);
			return erpResponse;
		}
		erpLog = ErpLog.ErpSuccess(contract.Id, Guid.Empty, ErpAction.Cancel, erpResponse.RequestPayload, erpResponse.ResponsePayload, erpResponse.ContractNumber);
		await _dbContext.ErpLog.AddAsync(erpLog, cancellationToken);
		await _dbContext.SaveChangesAsync(cancellationToken);

		// create a pricing for the remaining
		return await PriceContract(contract, agvantageId, cancellationToken: cancellationToken);
	}
	#endregion
}
