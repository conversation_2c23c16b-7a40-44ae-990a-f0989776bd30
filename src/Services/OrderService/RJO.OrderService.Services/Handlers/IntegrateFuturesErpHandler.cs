using LaunchDarkly.Sdk;
using LaunchDarkly.Sdk.Server;
using MediatR;
using RJO.BuildingBlocks.Common;
using RJO.OrderService.Services.DTO.ERP;
using RJO.OrderService.Services.Services;

namespace RJO.OrderService.Services.Handlers;

public class IntegrateFuturesErpQuery : IRequest<List<IntegrateFuturesErpResponseDto>>
{ 
	public Context FlagContext { get; set; }
	public IReadOnlyCollection<IntegrateFuturesErpRequestDto> Request { get; set; }
}

public class IntegrateFuturesErpHandler : IRequestHandler<IntegrateFuturesErpQuery, List<IntegrateFuturesErpResponseDto>>
{
	readonly MarketTransactionDomainService _marketTransactionDomainService;
	readonly LdClient _ldClient;

	public IntegrateFuturesErpHandler(MarketTransactionDomainService marketTransactionDomainService, LdClient ldClient)
	{ 
		_marketTransactionDomainService = marketTransactionDomainService;
		_ldClient = ldClient;
	}

	public async Task<List<IntegrateFuturesErpResponseDto>> Handle(IntegrateFuturesErpQuery request, CancellationToken cancellationToken)
	{ 
		var result = new List<IntegrateFuturesErpResponseDto>();
		
		if (_ldClient.BoolVariation(FeatureFlags.EnableHedgeIntegration, request.FlagContext))
		{
			result = await _marketTransactionDomainService.SendFillsListToErpOnDemand(request.Request.Select(a => a.Id).ToList());
		}
		
		return result;
	}
}
