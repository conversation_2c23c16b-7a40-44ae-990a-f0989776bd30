using MediatR;
using ClosedXML.Excel;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Common;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.DTO.Bidsheet;

namespace RJO.OrderService.Services.Handlers.Bidsheet;

public class ExportBidsheetQuery : IRequest<byte[]>, ITransactionalRequest
{
    public BidsheetFilterDto Filter { get; set; }
    public string Name { get; set; }
}

public class ExportBidsheetQueryHandler : IRequestHandler<ExportBidsheetQuery, byte[]>
{
    readonly BidsheetRepository _bidsheetRepository;
    readonly LocationRepository _locationRepository;
    readonly CommodityRepository _commodityRepository;

    public ExportBidsheetQueryHandler(BidsheetRepository bidsheetRepository, LocationRepository locationRepository, CommodityRepository commodityRepository)
    {
        _bidsheetRepository = bidsheetRepository;
        _locationRepository = locationRepository;
        _commodityRepository = commodityRepository;
    }

    public async Task<byte[]> Handle(ExportBidsheetQuery request, CancellationToken cancellationToken)
    {
        AssertionConcern.ArgumentIsNotNull(request.Filter, "Filter information can not be null");
        var query = from b in await _bidsheetRepository.GetAllEntities()
            join c in await _commodityRepository.GetAllEntities() on b.CommodityId equals c.Id
            join l in await _locationRepository.GetAllEntities() on b.DeliveryLocationId equals l.Id
            orderby c.Name, b.DeliveryStart, b.DeliveryEnd, l.Name
            select new ExportItemDto
            {
                CommodityName = c.Name,
                CommodityId = b.CommodityId,
                LocationName = l.Name,
                LocationId = b.DeliveryLocationId,
                StartDate = b.DeliveryStart,
                EndDate = b.DeliveryEnd,
                FutureMonth = b.FutureMonth,
                FuturesYearNumber = b.CropYear,
                Basis = b.Basis
            };
        if (request.Filter.Commodities != null && request.Filter.Commodities.Count > 0)
        {
            query = query.Where(x => request.Filter.Commodities.Contains(x.CommodityId));
        }

        if (request.Filter.Locations != null && request.Filter.Locations.Count > 0)
        {
            query = query.Where(x => request.Filter.Locations.Contains(x.LocationId));
        }

        if (request.Filter.CropYears != null && request.Filter.CropYears.Count > 0)
        {
            query = query.Where(x => request.Filter.CropYears.Contains(x.FuturesYearNumber));
        }

        request.Name = "Hrvystupload";
        using (var workbook = new XLWorkbook())
        {
            var ws = workbook.AddWorksheet(BidsheetConstants.BidsheetName);
            SetExcelTitles(ws);
            SetExcelValues(ws, query.ToList());
            using (var stream = new MemoryStream())
            {
                workbook.SaveAs(stream);
                return stream.ToArray();
            }
        }
    }

    static void SetExcelValues(IXLWorksheet ws, List<ExportItemDto> list)
    {
        var commodityId = Guid.Empty;
        var locationId = Guid.Empty;
        var start = DateTime.MinValue;
        var end = DateTime.MinValue;
        var row = 2;
        for (var i = 0; i < list.Count; i++)
        {
            var item = list[i];
            if (commodityId == item.CommodityId && locationId == item.LocationId)
            {
                if (start == item.StartDate && end == item.EndDate)
                {
                    continue;
                }
            }
            else
            {
                commodityId = item.CommodityId;
                locationId = item.LocationId;
            }

            start = item.StartDate;
            end = item.EndDate;
            ws.Cell(row, 1).Value = item.CommodityName;
            ws.Cell(row, 2).Value = item.LocationName;
            ws.Cell(row, 3).Value = item.StartDate;
            ws.Cell(row, 4).Value = item.EndDate;
            ws.Cell(row, 3).Style.NumberFormat.Format = "MM-dd-yyyy";
            ws.Cell(row, 4).Style.NumberFormat.Format = "MM-dd-yyyy";
			//ws.Cells(row, 5).Value = DateExtensions.GetFuturesMonthNumber(item.FutureMonth);
			//ws.Cells(row, 6).Value = item.FuturesYearNumber;
            ws.Cell(row, 5).Value = item.FutureMonth;
            ws.Cell(row, 6).Value = item.Basis;
            ws.Cell(row, 6).Style.NumberFormat.Format = "0.0000";
            row++;
        }

        ws.Column(1).AdjustToContents();
        ws.Column(2).AdjustToContents();
        ws.Column(3).AdjustToContents();
        ws.Column(4).AdjustToContents();
        ws.Column(5).AdjustToContents();
        ws.Column(6).AdjustToContents();
    }

    static void SetExcelTitles(IXLWorksheet ws)
    {
        ws.Cell(1, 1).Value = "CommodityName";
        ws.Cell(1, 2).Value = "LocationName";
        ws.Cell(1, 3).Value = "StartDate";
        ws.Cell(1, 4).Value = "EndDate";
		//ws.Cells(1, 5).Value = "FuturesMonthNumber";
		//ws.Cells(1, 6).Value = "FuturesYearNumber";
        ws.Cell(1, 5).Value = "MonthYear";
        ws.Cell(1, 6).Value = "Basis";
        ws.SheetView.FreezeRows(1);
    }
}
