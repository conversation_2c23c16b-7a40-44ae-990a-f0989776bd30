using LaunchDarkly.Sdk;
using LaunchDarkly.Sdk.Server;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.Extensions;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Common;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Bidsheet;
using RJO.OrderService.Services.DTO.Contract;
using RJO.OrderService.Services.DTO.ResultSets;
using RJO.OrderService.Services.Enumeration;
using RJO.OrderService.Services.Features.Notifications;
using RJO.OrderService.Services.Features.Notifications.AutoRefresh;
using RJO.OrderService.Services.Features.Notifications.Notifications.Commands;
using RJO.OrderService.Services.Helper;
using RJO.OrderService.Services.Services;
using RJO.OrderService.Services.Services.OrderProcess;

namespace RJO.OrderService.Services.Handlers;

public sealed record CreateContractCommand(ContractDto Contract, bool IncludeSalesTrading, Context FlagContext) : IRequest<string>, ITransactionalRequest;

public sealed class CreateContractCommandHandler : IRequestHandler<CreateContractCommand, string>
{
	readonly UnitOfWork _unitOfWork;
	readonly ContractWorkflowContext _services;
	readonly BidSheetHelper _bidSheetHelper;
	readonly ContractOfferValidationHelper _contractValidationHelper;
	readonly ErpIntegrationAgent _erpIntegrationAgent;
	readonly MetadataDomainService _metadataDomainService;
	readonly PreHedgeService _preHedgeService;
	readonly ILogger _logger;
	readonly IMediator _mediator;
	readonly AppDbContext _dbContext;
	readonly LdClient _ldClient;

	public CreateContractCommandHandler(UnitOfWork unitOfWork,
		ContractWorkflowContext services, BidSheetHelper bidSheetHelper,
		ContractOfferValidationHelper contractValidationHelper,
		ErpIntegrationAgent erpIntegrationAgent,
		MetadataDomainService metadataDomainService, ILogger<CreateContractCommandHandler> logger,
		IMediator mediator, AppDbContext dbContext, LdClient ldClient, PreHedgeService orphanedHedgeHelper)
	{
		_unitOfWork = unitOfWork;
		_services = services;
		_bidSheetHelper = bidSheetHelper;
		_contractValidationHelper = contractValidationHelper;
		_erpIntegrationAgent = erpIntegrationAgent;
		_metadataDomainService = metadataDomainService;
		_logger = logger;
		_mediator = mediator;
		_dbContext = dbContext;
		_ldClient = ldClient;
		_preHedgeService = orphanedHedgeHelper;
	}

	public async Task<string> Handle(CreateContractCommand request, CancellationToken cancellationToken)
	{
		var commodity = await _dbContext.Commodities
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == request.Contract.CommodityId, cancellationToken);
		if (_ldClient.BoolVariation(FeatureFlags.EnablePreHedge, request.FlagContext))
		{
			if (!request.Contract.PrehedgeId.IsNullOrEmpty())
			{
				var hedge = _dbContext.MarketTransactions.FirstOrDefault(x => x.Id == request.Contract.PrehedgeId && x.Source == EMarketTransactionSource.PreHedge);
				AssertionConcern.ArgumentIsNotNull(hedge, "The hedge provided could not be found");
				var assignedRegionId = await _preHedgeService.GetAssignedRegion(hedge.Id, cancellationToken);
				AssertionConcern.ArgumentIsNotTrue(assignedRegionId != Guid.Empty && assignedRegionId != request.Contract.RegionId, "A contract has been created from hedge with a different region");
				
				AssertionConcern.ArgumentIsNotEquals(request.Contract.IsSell, hedge.IsSell, request.Contract.IsSell ? "Cannot create a sell contract from a sell hedge" : "Cannot create a buy contract from a buy hedge");
				var maxLimit = await _preHedgeService.CalculateContractMaximumLimit(hedge.Id, hedge.WorkingLots, commodity.LotFactor);
				AssertionConcern.ArgumentIsSmallerOrEqualThan(request.Contract.Quantity, maxLimit, $"The contract quantity is above the limit of {maxLimit}");
				AssertionConcern.ArgumentIsTrue(request.Contract.ContractTypeId == ContractTypeDictionary.HTA || request.Contract.ContractTypeId == ContractTypeDictionary.FlatPrice,
					"Contract type must be either HTA or Flat Price");
			}
		}

		AssertionConcern.ArgumentIsNotNull(commodity.Name, ContractResources.ContractTypeValuesAreWrong);
		AssertionConcern.ArgumentIsTrue(await _dbContext.ContractTypes
			.AsNoTracking()
			.AnyAsync(x => x.Id == request.Contract.ContractTypeId, cancellationToken), ContractResources.ContractTypeValuesAreWrong);
		AssertionConcern.ArgumentIsTrue(await _dbContext.Employees
			.AsNoTracking()
			.AnyAsync(x => x.Id == request.Contract.EmployeeId, cancellationToken), ContractResources.ContractTypeValuesAreWrong);
		var locations = await _dbContext.Locations
			.AsNoTracking()
			.Where(x => x.Id == request.Contract.LocationId || x.Id == request.Contract.DeliveryLocationId)
			.ToListAsync(cancellationToken);
		AssertionConcern.ArgumentIsTrue(locations.Any(x => x.Id == request.Contract.LocationId), ContractResources.ContractTypeValuesAreWrong);
		AssertionConcern.ArgumentIsTrue(locations.Any(x => x.Id == request.Contract.LocationId), ContractResources.ContractTypeValuesAreWrong);
		ContractAssertionConcern.CropYearIsValid(request.Contract.CropYear);
		if (TransactionTypeDictionary.Adjustment != request.Contract.TransactionTypeId)
		{
			AssertionConcern.ArgumentIsNotNull(request.Contract.CustomerId, ContractResources.ContractTypeValuesAreWrong);
		}

		await _contractValidationHelper.ValidateOmsOnly();
		if (request.Contract.ContractTypeId != ContractTypeDictionary.HTA && !request.Contract.IsDeliveryDatesCustom)
		{
			BidSheetItemDto bidSheetInformation;
			var bidsheet = await _bidSheetHelper.GetInformationFromBidSheetExactValues(request.Contract.CommodityId, request.Contract.DeliveryLocationId, request.Contract.CropYear, request.Contract.DeliveryStartDate,
				request.Contract.FuturesMonth, request.Contract.DeliveryEndDate);
			if (bidsheet == null)
			{
				bidSheetInformation = await _bidSheetHelper.GetInformationFromBidSheetApproximateValues(request.Contract.CommodityId, request.Contract.DeliveryLocationId, request.Contract.CropYear, request.Contract.DeliveryStartDate,
					request.Contract.DeliveryEndDate);
				AssertionConcern.ArgumentIsNotNull(bidSheetInformation, ErrorCodes.BidsheetNotFound);
			}
			else
			{
				bidSheetInformation = new()
				{
					FuturesMonth = bidsheet.FutureMonth,
					PostedBasis = bidsheet.Basis
				};
			}

			AssertionConcern.ArgumentIsNotNull(request.Contract.NetBasis, "NetBasis value is required");
			AssertionConcern.ArgumentIsBetweenInclusive(request.Contract.NetBasis.Value, bidSheetInformation.PostedBasis + commodity.BasisControl, bidSheetInformation.PostedBasis - commodity.BasisControl,
				"Basis price is outside of set Basis Control");
			AssertionConcern.ArgumentIsTrue(request.Contract.PostedBasis == bidSheetInformation.PostedBasis, "Basis has changed, please re-input contract");
		}

		if (request.Contract.ContractTypeId != ContractTypeDictionary.Basis && request.Contract.ContractTypeId != ContractTypeDictionary.NTC)
		{
			AssertionConcern.ArgumentIsNotNull(request.Contract.FuturesPrice, "Price Control value is required");
		}

		var groupedLocationQuery = _dbContext.GroupedLocations
							.AsNoTracking()
							.Where(x => x.DestinationLocationId == request.Contract.DeliveryLocationId
										&& x.ContractLocationId == request.Contract.LocationId
										&& x.IsActive);

		if (request.Contract.RegionId != null)
		{
			groupedLocationQuery = groupedLocationQuery.Where(x => x.RegionId == request.Contract.RegionId);
		}

		var groupedLocation = await groupedLocationQuery.FirstOrDefaultAsync(cancellationToken);

		var regionId = request.Contract.RegionId;
		if (regionId == null)
		{
			regionId = groupedLocation?.RegionId;
		}

		await _contractValidationHelper.ValidateLimits(request.Contract.IsSell, request.Contract.DeliveryLocationId, request.Contract.Quantity, groupedLocation?.Id);

		var contract = new Contract(
			request.Contract.TransactionTypeId,
			request.Contract.ContractTypeId,
			request.Contract.IsSell,
			request.Contract.CommodityId,
			request.Contract.LocationId,
			request.Contract.DeliveryLocationId,
			request.Contract.IsDeliveryDatesCustom,
			request.Contract.DeliveryStartDate,
			request.Contract.DeliveryEndDate,
			request.Contract.CropYear,
			request.Contract.CustomerId,
			request.Contract.EmployeeId,
			request.Contract.FuturesMonth,
			request.Contract.FuturesPrice,
			request.Contract.PostedBasis,
			request.Contract.PushBasis,
			request.Contract.FreightPrice,
			request.Contract.Fees1,
			request.Contract.Fees2,
			request.Contract.Quantity,
			request.Contract.Comments,
			request.Contract.PassFill,
			request.Contract.DoNotHedge,
			request.Contract.CashSettlement,
			request.Contract.ExpirationDate,
			ContractSource.User,
			regionId
		);

		if (request.Contract.TransactionTypeId == TransactionTypeDictionary.SalesTrading)
		{
			AssertionConcern.ArgumentIsTrue(request.IncludeSalesTrading, "The user does not have permission for this operation.");
			contract.AssignContractNumber(request.Contract.Number);
		}

		if (request.Contract.TransactionTypeId == TransactionTypeDictionary.BushelOnly)
		{
			contract.AssignContractNumber(request.Contract.Number);
		}

		var result = new CreateContractResultDto();
		if (request.Contract.ContractTypeId != ContractTypeDictionary.NTC && request.Contract.PrehedgeId.IsNullOrEmpty())
		{
			result = await _services.ProcessContract(contract);

			if (request.Contract.ContractTypeId != ContractTypeDictionary.Basis)
			{
				if (request.Contract.PassFill)
				{
					AssertionConcern.ArgumentIsNotTrue(request.Contract.Quantity < 5000, "If pass fill is selected, the quantity must be a hedgeable value.");
					contract.ChangeFuturesPrice(null);
					contract.ChangePrice(null);
				}
			}
		}

		contract.AssignTheirContract(request.Contract.TheirContract);
		contract.AssignExtendedContractTypeId(request.Contract.ExtendedContractTypeId);
		await _metadataDomainService.CreateContractFields(contract.Id, request.Contract.CustomFields);
		_dbContext.Contracts.Add(contract);

		foreach (var transaction in result.Transactions)
		{
			transaction.ProcessEvent();
		}

		if (_ldClient.BoolVariation(FeatureFlags.EnablePreHedge, request.FlagContext))
		{
			if (!request.Contract.PrehedgeId.IsNullOrEmpty())
			{
				var hedge = await _dbContext.MarketTransactions.FirstOrDefaultAsync(m => m.Id == request.Contract.PrehedgeId.Value, cancellationToken);
				var remaining = contract.Quantity - hedge.OrphanedBalance ?? 0;
				var availableBalance = request.Contract.Quantity > hedge.OrphanedBalance ? hedge.OrphanedBalance.Value : request.Contract.Quantity;
				var preHedgeContract = new PreHedgeContract(request.Contract.PrehedgeId.Value, contract.Id, availableBalance);
				_dbContext.PreHedgeContracts.Add(preHedgeContract);
				hedge.ReduceOrphanedBalance(availableBalance);
				if (hedge.OrphanedBalance == 0)
				{
					var doNotHedgeLog = await _dbContext
												.DoNotHedgeLog
												.AsNoTracking()
												.OrderByDescending(a => a.CreatedOn)
												.FirstOrDefaultAsync(a => a.CommodityId == contract.CommodityId && a.CropYear == contract.RealCropYear && a.RegionId == contract.RegionId, cancellationToken);
					var balance = await GetOrCreateBalance(request.Contract.CommodityId, request.Contract.RegionId, contract.RealCropYear);
					var prehedgeContracts = await _dbContext.PreHedgeContracts
						.Include(p => p.Contract)
						.Where(p => p.MarketTransactionId == hedge.Id)
						.OrderBy(c => c.CreatedOn)
						.ToListAsync(cancellationToken);
					foreach (var item in prehedgeContracts)
					{
						balance.CalculateBalanceWithoutUpdate(item.QuantityAssigned, item.Contract.IsSell, item.Contract.Id, Enumeration.ETransactionEvent.Create.ToString(), doNotHedgeLog is { IsActive : true}, false, false, item.Contract.LocationId, false, item.Contract.DeliveryStartDate,
						   item.Contract.FuturesMonth, false, request.Contract.RegionId);
						balance.UpdateBalance(item.QuantityAssigned, request.Contract.IsSell);
					}
					balance.CalculateBalanceWithoutUpdate(request.Contract.Quantity, request.Contract.IsSell, contract.Id, Enumeration.ETransactionEvent.Create.ToString(), doNotHedgeLog is { IsActive: true }, false, false, request.Contract.LocationId, false, request.Contract.DeliveryStartDate,
						   request.Contract.FuturesMonth, false, request.Contract.RegionId);
					balance.UpdateBalance(request.Contract.Quantity, request.Contract.IsSell);
					balance.UpdateBalance(hedge.Quantity, !request.Contract.IsSell);
					hedge.CreateLedgerEvent(hedge.Quantity, balance.Balance(), Enumeration.ETransactionEvent.AssignFutures.ToString(), ETransactionType.Hedge.ToString(), request.Contract.RegionId);

					remaining = balance.Balance() + remaining;
					if (remaining != 0 && doNotHedgeLog is not { IsActive: true })
					{
						var preHedgeResult = await _services.ProcessPrehedgeContract(contract, remaining);
						foreach (var transaction in preHedgeResult.Transactions)
						{
							transaction.ProcessEvent();
						}
					}
				}
			}
		}
		if (_ldClient.BoolVariation(FeatureFlags.UseBackgroundTaskQueue, request.FlagContext))
		{
			await _unitOfWork.CommitToBackgroundTaskQueueAsync();
		}
		else
		{
			await _unitOfWork.CommitAsync();
		}

		if (_ldClient.BoolVariation(FeatureFlags.EnableAutoRefresh, request.FlagContext))
		{
			var contractInfo = await _mediator.Send(new GetContractByIdQuery(contract.Id, request.FlagContext), cancellationToken);
			await _mediator.Send(new SendPushNotificationCommand<ContractInfoDto>(Constants.ContractChannel, new(ActionType.Insert, contractInfo)), cancellationToken);
			await _mediator.Send(new SendPushNotificationCommand<string>(Constants.ReviewAndReleaseChannel, new(ActionType.Insert, "")), cancellationToken);
			if (!request.Contract.PrehedgeId.IsNullOrEmpty())
			{
				await _mediator.Send(new SendPushNotificationCommand<string>(Constants.PreHedgeChannel, new(ActionType.Insert, "")), cancellationToken);
			}
		}

		// ERP operation
		if (contract.ShouldSendContractErp())
		{
			_logger.LogInformation("CreateContractCommandHandler: Sending ERP contract {InternalCode}", contract.InternalCode);
			await _erpIntegrationAgent.ProcessCreation(contract);
		}
		else
			_logger.LogInformation("CreateContractCommandHandler: Ignoring ERP contract {InternalCode}", contract.InternalCode);

		return contract.InternalCode;
	}

	async Task<BucketBalance> GetOrCreateBalance(Guid commodityId, Guid? regionId, short cropYear)
	{
		var balance = await _dbContext.BucketBalance
			.FirstOrDefaultAsync(x => x.CommodityId == commodityId && x.RegionId == regionId && x.CropYear == cropYear);
		if (balance == null)
		{
			balance = new(commodityId, regionId, cropYear);
			_dbContext.BucketBalance.Add(balance);
		}
		else
		{
			balance.TotalOriginalBalance();
		}

		return balance;
	}
}
