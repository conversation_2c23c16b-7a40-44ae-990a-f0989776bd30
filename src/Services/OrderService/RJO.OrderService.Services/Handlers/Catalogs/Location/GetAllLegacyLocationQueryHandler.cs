using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Common;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Extensions;
using RJO.OrderService.Services.DTO.Catalogs.Location;
using RJO.OrderService.Services.DTO.Common;
using RJO.OrderService.Services.Extensions.Validator;

namespace RJO.OrderService.Services.Handlers.Catalogs.Location;

public sealed class GetAllLegacyLocationQuery : IRequest<ListDto<LegacyLocationItemDto>>
{
	public LocationFilterDto Filter { get; set; }
}

public sealed class GetAllLegacyLocationQueryHandler : IRequestHandler<GetAllLegacyLocationQuery, ListDto<LegacyLocationItemDto>>
{
	readonly ILogger<GetAllLocationQueryHandler> _logger;
	readonly AppDbContext _dbContext;

	public GetAllLegacyLocationQueryHandler(ILogger<GetAllLocationQueryHandler> logger, AppDbContext dbContext)
	{
		_logger = logger;
		_dbContext = dbContext;
	}

	public async Task<ListDto<LegacyLocationItemDto>> Handle(GetAllLegacyLocationQuery request, CancellationToken cancellationToken)
	{
		if (!request.Filter.ValidatePagingParametersAreGreaterThanZero())
			throw new PaginationNotValidException("Pagination parameters are not valid, please review your parameters");

		var allLocations = _dbContext.Locations
			.Select(x => new
			{
				x.Id,
				x.Name,
				x.Number,
				x.IsDestination,
				x.IsLocation,
				x.IsActive
			});

		if (!string.IsNullOrEmpty(request.Filter.Name))
		{
			allLocations = allLocations.Where(x => x.Name.Contains(request.Filter.Name));
		}

		allLocations = !string.IsNullOrEmpty(request.Filter.SortColumnName) ? allLocations.OrderByDynamic(request.Filter.SortColumnName, request.Filter.SortOrder == SortOrder.Ascending) : allLocations.OrderBy(x => x.Name);

		var count = await allLocations.CountAsync(cancellationToken);
		allLocations = allLocations.GetPagedQuery(request.Filter.Start, request.Filter.Limit);
		_logger.LocationAll();

		var lst = new List<LegacyLocationItemDto>(count);

		foreach (var item in allLocations.ToList())
		{
			var contractCount = await _dbContext.Contracts.CountAsync(c => c.LocationId == item.Id || c.DeliveryLocationId == item.Id, cancellationToken);
			var offerCount = await _dbContext.Offers.CountAsync(o => o.LocationId == item.Id || o.DeliveryLocationId == item.Id, cancellationToken);
			var isUsed = contractCount > 0 || offerCount > 0;

			var itemDto = new LegacyLocationItemDto
			{
				Id = item.Id,
				Name = item.Name,
				Number = item.Number,
				IsLocation = item.IsLocation,
				IsDestination = item.IsDestination,
				IsActive = item.IsActive,
				IsUsed = isUsed
			};
			lst.Add(itemDto);
		}
		
		return new(count, lst);
	}
}
