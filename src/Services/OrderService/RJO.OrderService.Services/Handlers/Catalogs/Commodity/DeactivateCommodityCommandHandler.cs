using MediatR;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Repositories;

namespace RJO.OrderService.Services.Handlers.Catalogs.Commodity;

public class DeactivateCommodityCommand : IRequest<Unit>, ITransactionalRequest
{
	public Guid CommodityId { get; set; }
}

public class DeactivateCommodityCommandHandler : IRequestHandler<DeactivateCommodityCommand, Unit>
{
	readonly CommodityRepository _commodityRepository;
	readonly UnitOfWork _unitOfWork;

	public DeactivateCommodityCommandHandler(CommodityRepository commodityRepository, UnitOfWork unitOfWork)
	{
		_commodityRepository = commodityRepository;
		_unitOfWork = unitOfWork;
	}

	public async Task<Unit> Handle(DeactivateCommodityCommand request, CancellationToken cancellationToken)
	{
		var commodity = await _commodityRepository.GetSingleOrDefault(x => x.Id == request.CommodityId);
		AssertionConcern.ArgumentIsNotNull(commodity, $"Commodity with GUID '{request.CommodityId}' does not exist");

		commodity.Deactivate();

		_commodityRepository.Update(commodity);
		await _unitOfWork.CommitAsync();
		return Unit.Value;
	}
}
