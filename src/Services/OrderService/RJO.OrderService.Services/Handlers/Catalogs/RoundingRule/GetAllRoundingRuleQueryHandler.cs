using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO;

namespace RJO.OrderService.Services.Handlers.Catalogs.RoundingRule;

public class GetAllRoundingRuleQuery : IRequest<List<RoundingRuleDto>>
{
}

public class GetAllRoundingRuleQueryHandler : IRequestHandler<GetAllRoundingRuleQuery, List<RoundingRuleDto>>
{
	readonly AppDbContext _dbContext;

	public GetAllRoundingRuleQueryHandler(AppDbContext dbContext) => _dbContext = dbContext;

	public async Task<List<RoundingRuleDto>> Handle(GetAllRoundingRuleQuery request, CancellationToken cancellationToken)
	{
		var rules = await _dbContext.RoundingRules
			.Join(
				_dbContext.ContractTypes,
				rr => rr.ContractTypeId,
				ct => ct.Id,
				(rr, ct) => new
				{
					RoundingRule = rr,
					ContractType = ct
				}
			)
			.Select(mapping => new RoundingRuleDto
			{
				ContractTypeId = mapping.RoundingRule.ContractTypeId,
				DecimalPlace = mapping.RoundingRule.DecimalPlace,
				Id = mapping.RoundingRule.Id,
				IsSell = mapping.RoundingRule.IsSell,
				RoundingTypeId = mapping.RoundingRule.RoundingType.Value,
				From = mapping.RoundingRule.From,
				To = mapping.RoundingRule.To,
				IsActive = mapping.RoundingRule.IsActive,
				ContractTypeName = mapping.ContractType.Name == "Basis" ? "Basis Pricing" :
								   mapping.ContractType.Name == "HTA" ? "HTA Create" :
								   mapping.ContractType.Name
			})
			.ToListAsync(cancellationToken);


		return rules.ToList();
	}
}
