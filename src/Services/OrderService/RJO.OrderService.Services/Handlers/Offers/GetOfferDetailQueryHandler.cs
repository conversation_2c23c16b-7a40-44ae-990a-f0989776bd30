using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Metadata;
using RJO.OrderService.Services.DTO.Offer;
using RJO.OrderService.Services.Helper;
using RJO.OrderService.Services.Services;

namespace RJO.OrderService.Services.Handlers.Offers;
public class GetOfferDetailQuery : IRequest<OfferDetailDto>
{
	public Guid Id { get; init; }
}
public sealed class GetOfferDetailQueryHandler : IRequestHandler<GetOfferDetailQuery, OfferDetailDto>
{
	readonly MarketStatusService _marketStatusService;
	readonly AppDbContext _dbContext;

	public GetOfferDetailQueryHandler(MarketStatusService marketStatusService, AppDbContext dbContext)
	{
		_marketStatusService = marketStatusService;
		_dbContext = dbContext;
	}

	public async Task<OfferDetailDto> Handle(GetOfferDetailQuery request, CancellationToken cancellationToken)
	{
		var offerDetail = await (from f in _dbContext.Offers
								 where f.Id == request.Id
								 join b in _dbContext.Employees on f.EmployeeId equals b.Id
								 join up in _dbContext.Employees on f.UpdatedEmployeeId equals up.Id
								 join cu in _dbContext.Customers on f.CustomerId equals cu.Id into gcu
								 from customer in gcu.DefaultIfEmpty()
								 join l in _dbContext.Locations on f.LocationId equals l.Id
								 join c in _dbContext.Commodities on f.CommodityId equals c.Id
								 join t in _dbContext.TransactionTypes on f.TransactionTypeId equals t.Id
								 join ct in _dbContext.ContractTypes on f.ContractTypeId equals ct.Id
								 join dl in _dbContext.Locations on f.DeliveryLocationId equals dl.Id
								 join r in _dbContext.Regions on f.RegionId equals r.Id into reg
								 from regions in reg.DefaultIfEmpty()
								 select new
								 {
									 f.Number,
									 f.FuturesMonth,
									 f.Expiration,
									 f.Gtc,
									 f.CreatedOn,
									 f.PushBasis,
									 f.NetBasis,
									 f.PostedBasis,
									 f.DeliveryStartDate,
									 f.DeliveryEndDate,
									 f.HasRejection,
									 f.Id,
									 f.Price,
									 f.FreightPrice,
									 f.FuturesPrice,
									 f.IsSell,
									 f.Quantity,
									 f.RemainingBalance,
									 f.CropYear,
									 f.RealCropYear,
									 f.Fees1,
									 f.Fees2,
									 f.CashSettlement,
									 f.UpdatedOn,
									 f.Event,
									 f.ContractParentId,
									 Status = f.Status.Value,
									 EmployeeLastName = b.LastName,
									 EmployeeFirstName = b.FirstName,
									 UpdateEmployeeLastName = up.LastName,
									 UpdateEmployeeFirstName = up.FirstName,
									 CustomerFirstName = customer.FirstName,
									 CustomerLastName = customer.LastName,
									 CustomerPhoneNumber = customer.PhoneNumber,
									 CustomerCountry = customer.Country,
									 CustomerState = customer.State,
									 CustomerCity = customer.City,
									 CustomerStreet = customer.Street,
									 CustomerZipCode = customer.ZipCode,
									 CustomerNumber = customer.Number,
									 CustomerWorkPhoneNumber = customer.WorkPhoneNumber,
									 CustomerEmail = customer.Email,
									 CommodityName = c.Name,
									 f.CustomerId,
									 LocationName = l.Name,
									 f.CommodityId,
									 TransactionTypeName = t.Name,
									 ContractTypeName = ct.Name,
									 ContractTypeCode = ct.Code,
									 DeliveryLocationName = dl.Name,
									 ContractTypeId = ct.Id,
									 f.Instrument,
									 f.IsInternal,
									 f.TheirContract,
									 regionName = regions == null ? "" : regions.Name,
								 }).FirstOrDefaultAsync(cancellationToken);
		if (offerDetail == null)
		{
			return null;
		}

		var isMarketOpen = await _marketStatusService.IsMarketOpen(offerDetail.Instrument);
		var dto = new OfferDetailDto
		{
			IsSell = offerDetail.IsSell,
			CreatedOn = offerDetail.CreatedOn,
			CreatedBy = offerDetail.EmployeeFirstName + " " + offerDetail.EmployeeLastName,
			UpdatedOn = offerDetail.UpdatedOn,
			UpdatedBy = offerDetail.UpdatedOn == null ? null : offerDetail.UpdateEmployeeFirstName + " " + offerDetail.UpdateEmployeeLastName,
			DeliveryStartDate = offerDetail.DeliveryStartDate,
			DeliveryEndDate = offerDetail.DeliveryEndDate,
			Quantity = offerDetail.Quantity,
			FuturesMonth = offerDetail.FuturesMonth,
			FuturesPrice = offerDetail.FuturesPrice,
			NetBasis = offerDetail.NetBasis,
			PostedBasis = offerDetail.PostedBasis,
			PushBasis = offerDetail.PushBasis,
			FreightPrice = offerDetail.FreightPrice,
			Price = offerDetail.Price,
			Comments = null,
			Status = Helper.GetStatus(offerDetail.Status, offerDetail.Event),
			Fees1 = offerDetail.Fees1,
			Fees2 = offerDetail.Fees2,
			EmployeeName = $"{offerDetail.EmployeeLastName} {offerDetail.EmployeeFirstName}",
			CommodityName = offerDetail.CommodityName,
			CropYear = offerDetail.CropYear,
			RealCropYear = offerDetail.RealCropYear,
			CustomerName = $"{offerDetail.CustomerNumber} - {offerDetail.CustomerFirstName} {offerDetail.CustomerLastName}",
			CustomerWorkPhoneNumber = offerDetail.CustomerWorkPhoneNumber,
			CustomerPhoneNumber = offerDetail.CustomerPhoneNumber,
			CustomerEmail = offerDetail.CustomerEmail,
			LocationName = offerDetail.LocationName,
			DeliveryLocationName = offerDetail.DeliveryLocationName,
			TransactionTypeName = offerDetail.TransactionTypeName,
			Id = offerDetail.Id,
			Number = offerDetail.Number,
			Unit = "Bushels",
			Expiration = offerDetail.Expiration,
			CashSettlement = offerDetail.CashSettlement,
			ContractTypeName = offerDetail.ContractTypeName,
			Gtc = offerDetail.Gtc,
			RegionName = offerDetail.regionName,
			Restrictions = new()
			{
				CanBeEdited = OfferActionsHelper.CanBeEdited(offerDetail.Status, offerDetail.Event, offerDetail.ContractTypeId, isMarketOpen),
				CanBeCanceled = OfferActionsHelper.CanBeCanceled(offerDetail.Status, offerDetail.Event, isMarketOpen),
				CanBeBooked = OfferActionsHelper.CanBeBooked(offerDetail.Status, offerDetail.Event, isMarketOpen),
				CanBeCompleted = OfferActionsHelper.CanBeCompleted(offerDetail.Status)
			},
			UnFilled = offerDetail.RemainingBalance,
			CustomFields = await GetCustomFields(request.Id),
			TheirContract = offerDetail.TheirContract
		};
		if (offerDetail.ContractParentId.HasValue && offerDetail.ContractTypeId != ContractTypeDictionary.FlatPrice && offerDetail.Status != EOfferState.Filled)
		{
			if (offerDetail.ContractTypeId == ContractTypeDictionary.Basis)
			{
				dto.FuturesPrice = 0;
				dto.History = await GetHistory(offerDetail.Id, false, true);
			}
			else
			{
				dto.PushBasis = 0;
				dto.PostedBasis = 0;
				dto.NetBasis = 0;
				dto.History = await GetHistory(offerDetail.Id, true, false);
			}
		}
		else
		{
			dto.History = await GetHistory(offerDetail.Id, true, true);
		}

		if (offerDetail.Status == EOfferState.PartiallyFilled)
		{
			dto.Status = EOfferState.Working.ToString();
		}

		return dto;
	}

	async Task<IList<OrderMetadataValueDto>> GetCustomFields(Guid offerId)
	{
		var customFields = await (from l in _dbContext.OrderMetadataConfiguration
								  join h in _dbContext.OfferMetadata on l.Id equals h.FieldId into fields
								  from be in fields.DefaultIfEmpty()
								  where be.OfferId == offerId && l.IsActive
								  select new OrderMetadataValueDto(l.Label, be.Value, MetadataHelper.ContractTypeMap(l.Type)))
								 .ToListAsync();

		return customFields;
	}

	async Task<List<OfferHistoryItemDto>> GetHistory(Guid parentId, bool showFuturesPrice, bool showBasis)
	{
		var historicalData = await (from c in _dbContext.HistoricalOffers
									join cu in _dbContext.Customers on c.CustomerId equals cu.Id
									where c.OfferId == parentId
									orderby c.CreatedOn descending
									select new OfferHistoryItemDto
									{
										Id = c.Id,
										Basis = new()
										{
											Net = showBasis ? c.NetBasis : 0,
											Posted = showBasis ? c.PostedBasis : 0,
											Push = showBasis ? c.PushBasis : 0
										},
										Comments = c.Comments,
										CommodityName = c.CommodityName,
										Contract = new()
										{
											Number = c.Number,
											Type = c.Type,
											InternalNumber = c.InternalCode
										},
										CreatedOn = c.CreatedOn,
										CropYear = c.CropYear,
										CustomerName = c.CustomerName,
										CustomerNumber = cu.Number,
										LocationName = c.LocationName,
										Delivery = new()
										{
											Begin = c.DeliveryStartDate,
											End = c.DeliveryEndDate,
											Destination = c.DeliveryLocationName
										},
										Employee = new()
										{
											FirstName = c.EmployeeFirstName,
											LastName = c.EmployeeLastName
										},
										Futures = showFuturesPrice ? c.FuturesPrice : 0,
										FuturesMonth = c.FuturesMonth,
										IsSell = c.IsSell,
										Status = Helper.GetStatus(c.Status, c.Event),
										Price = c.Price,
										Quantity = new()
										{
											Quantity = c.Quantity,
											Unfilled = c.RemainingBalance
										},
										Event = new()
										{
											Name = Helper.GetEvent(c.Event),
											Description = Helper.GetDescriptionEvent(c.Event)
										},
										Expiration = c.Expiration,
										Gtc = c.Gtc,
										UpdatedOn = c.UpdatedOn,
										Fees1 = c.Fees1,
										Fees2 = c.Fees2,
										FreightPrice = c.FreightPrice
									}).ToListAsync();
		for (var i = 1; i < historicalData.Count; i++)
		{
			historicalData[i - 1].IsEditReject = historicalData[i - 1].Event.Name == "RollBack" && historicalData[i].Event.Name == "Edit";
		}

		return historicalData;
	}
}
