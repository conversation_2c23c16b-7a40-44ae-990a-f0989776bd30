using MediatR;
using Microsoft.Extensions.Logging;
using RJO.OrderService.Domain.Events;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Services.Services.OrderProcess;

namespace RJO.OrderService.Services.DomainEventHandlers;

public sealed class ContractErpProcessingHandler(
	ContractRepository contractRepository,
	ErpIntegrationAgent erpIntegrationAgent,
	LogWorkflowRepository logWorkflow,
	ILogger<ContractErpProcessingHandler> logger)
	: INotificationHandler<ContractErpProcessingRequested>
{
	public async Task Handle(ContractErpProcessingRequested notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("ContractErpProcessingHandler started for Contract {ContractId}", notification.ContractId);

		var contract = await contractRepository.GetById(notification.ContractId);
		if (contract == null)
		{
			logger.LogWarning("Contract {ContractId} not found for ERP processing", notification.ContractId);
			return;
		}

		logWorkflow.SetContract(contract.Id);
		await logWorkflow.AddLog("ContractErpProcessingHandler", "Starting background ERP processing via domain event");

		if (string.Equals(notification.ContractEvent, EContractEvent.Price.ToString(), StringComparison.Ordinal))
		{
			await logWorkflow.AddLog("ContractErpProcessingHandler", "Processing pricing in background");
			await erpIntegrationAgent.ProcessPricing(contract, true);
		}
		else if (string.Equals(notification.ContractEvent, EContractEvent.Create.ToString(), StringComparison.Ordinal))
		{
			await logWorkflow.AddLog("ContractErpProcessingHandler", "Processing creation in background");
			await erpIntegrationAgent.ProcessCreation(contract, true);
		}
		else if (string.Equals(notification.ContractEvent, EContractEvent.Edit.ToString(), StringComparison.Ordinal))
		{
			await logWorkflow.AddLog("ContractErpProcessingHandler", "Processing update in background");
			await erpIntegrationAgent.ProcessUpdate(contract, notification.QuantityChanged, notification.OriginalContract, asyncCall: true);
		}
		else if (string.Equals(notification.ContractEvent, EContractEvent.Undo.ToString(), StringComparison.Ordinal))
		{
			await logWorkflow.AddLog("ContractErpProcessingHandler", "Processing adjust in background");
			await erpIntegrationAgent.ProcessAdjust(contract, notification.QuantityChanged, notification.UpdateParent, asyncCall: true);
		}

		await logWorkflow.AddLog("ContractErpProcessingHandler", "Background ERP processing completed successfully");
		logger.LogInformation("ContractErpProcessingHandler completed successfully for Contract {ContractId}", notification.ContractId);
    }
}
