using RJO.OrderService.Domain;
using System.Collections.Immutable;

namespace RJO.OrderService.Services.DTO.ERP;

public class FuturesErpFilterDto
{
	public int Start { get; init; }
	public int Limit { get; init; }
	public DateTime? StartDate { get; init; }
	public DateTime? EndDate { get; init; }
	public string AccountNo { get; init; }
	public string OrderNo { get; init; }
	public ImmutableList<string> FuturesMonth { get; init; }
	public ImmutableList<ErpStatus> ErpStatus { get; init; }
	public ImmutableList<Guid> CommodityId { get; init; }
}
