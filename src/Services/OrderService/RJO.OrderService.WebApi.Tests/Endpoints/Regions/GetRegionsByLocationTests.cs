using FluentAssertions;
using LaunchDarkly.Sdk.Server;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Testing;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Services.DTO.Region;
using TestStack.BDDfy;
using TestStack.BDDfy.Xunit;
using Xunit.Abstractions;

namespace RJO.OrderService.WebApi.Tests.Endpoints.Regions;

public class GetRegionsByLocationTests : EndpointOutboundHttpTests
{
	readonly TenantData _tenantData = TestData.MidIowa;
	FakeUser _fakeUser;
	Guid _locationId;
	Guid _destinationId;

	public GetRegionsByLocationTests(OrderService fixture, ITestOutputHelper outputHelper) : base(fixture, outputHelper) { }

	[BddfyFact]
	void Scenario1()
	{
		_fakeUser = _tenantData.FakeUsers.Alice;

		this
			.Given(x => x.ValidRequestParameters(_tenantData.Locations.Deerfield.Id, _tenantData.Locations.LakeFalls.Id))
			.When(x => x.SendingRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheExpectedDataIsReturned())
			.BDDfy();
	}

	void ValidRequestParameters(Guid locationId, Guid destinationId)
	{
		_locationId = locationId;
		_destinationId = destinationId;
	}

	async Task SendingRequest() => Response = await Client.AuthenticatedAs(_fakeUser).GetAsync($"/api/region/getbylocation?locationId={_locationId}&destinationId={_destinationId}");

	Task TheExpectedDataIsReturned() =>
		Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var content = await Response.Content.ReadResultData<List<RegionItem>>();
			content.Should().NotBeNull();
			content = content.OrderBy(r => r.Id).ToList();

			var expectedData = await dbContext.GroupedLocations
				.Include(g => g.Region)
				.Where(g => g.ContractLocationId == _locationId && g.DestinationLocationId == _destinationId && g.IsActive && g.Region.IsEnabled)
				.Select(g => new RegionItem { Id = g.Region.Id, Name = g.Region.Name, IsEnabled = g.Region.IsEnabled, ErpNumber = g.Region.ErpNumber, })
				.OrderBy(g => g.Id)
				.ToListAsync();
			content.Count.Should().Be(expectedData.Count);
			for (var i = 0; i < expectedData.Count; i++)
			{
				expectedData[i]!.Id.Should().Be(content[i].Id);
				expectedData[i]!.Name.Should().Be(content[i].Name);
				expectedData[i]!.ErpNumber.Should().Be(content[i].ErpNumber);
				expectedData[i]!.IsEnabled.Should().Be(content[i].IsEnabled);
			}
		});
}
