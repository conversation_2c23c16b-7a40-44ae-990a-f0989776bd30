using FluentAssertions;
using LaunchDarkly.Sdk.Server;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using RJO.BuildingBlocks.Common;
using RJO.IntegrationEvents.Commons.Events;
using RJO.OrderService.Common;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence;
using RJO.OrderService.Services.Services;
using RJO.OrderService.Services.Services.OfferProcess.OfferLogic;
using RJO.OrderService.WebApi.Testing;
using RJO.OrderService.WebApi.Tests.Endpoints;
using System.Collections.Immutable;
using TestStack.BDDfy;
using TestStack.BDDfy.Xunit;
using Xunit.Abstractions;

namespace RJO.OrderService.WebApi.Tests.Services;

public class TransactionResendOnBidsheetRollTests : EndpointOutboundHttpTests
{
	readonly TenantData _tenantData = TestData.For(TenantNames.PoinsettRice);
	Guid _offerId;
	MarketTransaction _marketTransaction;

	public TransactionResendOnBidsheetRollTests(OrderService fixture, ITestOutputHelper outputHelper) : base(fixture, outputHelper)
	{
	}

	[BddfyFact]
	void Scenario1() => this
		.Given(x => x.AnOfferIsSentToMarket(5.5m, 15000))
		.And(x => x.WorkingOrderEventIsReceived())
		.And(x => x.BypassIsOff())
		.When(x => x.OfferIsUpdatedFromBidsheet())
		.And(x => x.CancelOrderEventIsReceived())
		.Then(x => x.Test())
		.BDDfy();

	async Task AnOfferIsSentToMarket(decimal price, int amount) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var offer = Create.FlatPriceOffer(_tenantData, _tenantData.Commodities.For(CommodityNames.Corn), _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.LarryAdams, _tenantData.Employees.Bob, price, amount, false, _tenantData.Regions.Default.Id);
			offer.CreateEntity(_tenantData.Employees.Bob.Email);
			_offerId = offer.Id;
			_marketTransaction = Create.MarketTransaction(_tenantData, offer).Approved();
			dbContext.Set<Offer>().Add(offer);
			dbContext.Set<MarketTransaction>().Add(_marketTransaction);
			_marketTransaction.SentToTheMarket();
			await dbContext.SaveChangesAsync();
		});

	async Task BypassIsOff() =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var setting = await dbContext.Set<Setting>().FirstOrDefaultAsync(x => x.Type == ESettingsType.Tenant && x.Name == "ByPass");
			if (setting == null)
				return;
			setting.UpdateValue("False");
			await dbContext.SaveChangesAsync();
		});

	async Task OfferIsUpdatedFromBidsheet() =>
		await Fixture.WithServiceProvider(_tenantData, async serviceProvider =>
			await Fixture.WithDbContextFor(_tenantData, async dbContext =>
			{
				var offer = await dbContext.Offers.FindAsync(_offerId);
				var commodity = _tenantData.Commodities.For(CommodityNames.Corn);
				var product = _tenantData.Products.GetById(commodity.ProductId);
				var futuresMonth = product.FutureMonths.Split(',').First(f => !offer.FuturesMonth.StartsWith(f, StringComparison.OrdinalIgnoreCase));
				if (offer != null)
				{
					offer.ChangeFuturesMonth(futuresMonth + offer.CropYear.ToString()[2..]);
					var unitOfWork = serviceProvider.GetRequiredService<UnitOfWork>();
					var offerLogic = ActivatorUtilities.GetServiceOrCreateInstance<MarketOfferLogic>(serviceProvider);
					await offerLogic.EditOfferFromBidsheet(offer);
					var marketTransaction = await dbContext.MarketTransactions.FindAsync(_marketTransaction.Id);
					marketTransaction?.StartCancelation();
					await unitOfWork.CommitAsync();
				}
			})
		);

	async Task WorkingOrderEventIsReceived() =>
		await Fixture.WithServiceProvider(_tenantData, async serviceProvider =>
		{
			var marketTransactionDomainService = serviceProvider.GetService<MarketTransactionDomainService>();
			var workingStatus = new WorkingOrderEvent
			{
				Status = "Working",
				ClOrderId = _marketTransaction.InternalCode,
				FillQty = 0,
				FillCnt = 0,
				MarketOrderId = "**********",
				MarketChainOrderId = "**********",
				MarketClientOrderId = _marketTransaction.ClientNumber,
				AccountId = "11111",
				UserId = "Hrvyst-CQG TEST",
				MarketScaledAvgFillPrice = 5500,
				MarketAvgFillPriceCorrect = 5.5,
				MarketRejectedReason = "",
				MarketIsRejected = false,
				MarketOrderType = "Limit",
				MarketLimitPrice = 5.5,
				TenantId = _tenantData.Id,
				FillPriceCorrect = 5.5,
				Fills = ImmutableList.Create<OrderStatusFill>()
			};
			await marketTransactionDomainService.MarkAsWorkingTransaction(workingStatus);
		});

	async Task CancelOrderEventIsReceived() =>
		await Fixture.WithServiceProvider(_tenantData, async serviceProvider =>
		{
			var marketTransactionDomainService = serviceProvider.GetService<MarketTransactionDomainService>();
			var cancelOrderEvent = new CancelOrderEvent
			{
				Status = "Cancelled",
				ClOrderId = _marketTransaction.InternalCode,
				FillQty = 0,
				FillCnt = 0,
				MarketOrderId = "**********",
				MarketChainOrderId = "**********",
				MarketClientOrderId = _marketTransaction.ClientNumber,
				AccountId = "11111",
				UserId = "Hrvyst-CQG TEST",
				MarketScaledAvgFillPrice = 5500,
				MarketAvgFillPriceCorrect = 5.5,
				MarketRejectedReason = "",
				MarketIsRejected = false,
				MarketOrderType = "Limit",
				MarketLimitPrice = 5.5,
				TenantId = _tenantData.Id,
				FillPriceCorrect = 5.5,
				Fills = ImmutableList.Create<OrderStatusFill>()
			};
			await marketTransactionDomainService.CancelTransaction(cancelOrderEvent);
		});

	async Task Test() =>
		await Fixture.WithServiceProvider(_tenantData, async serviceProvider =>
			await Fixture.WithDbContextFor(_tenantData, async dbContext =>
			{
				var statusService = serviceProvider.GetRequiredService<MarketStatusService>();
				var ldClient = serviceProvider.GetRequiredService<LdClient>();
				var flagIsOn = ldClient.BoolVariation(FeatureFlags.EnableTag50OnBidRollFix, LaunchDarkly.Sdk.User.WithKey("system"));

				var marketTransactions = await dbContext.MarketTransactions.Where(m => m.OfferId == _offerId).AsNoTracking().ToListAsync();
				marketTransactions.Count.Should().Be(2);
				var oldTransaction = marketTransactions.First(x => x.Id == _marketTransaction.Id);
				var newTransaction = marketTransactions.First(x => x.Id != _marketTransaction.Id);
				oldTransaction.State.Value.Should().Be(EMarketTransactionState.Canceled);
				newTransaction.Event.Should().Be(ETransactionEvent.Creation);

				var session = await statusService.GetStatusForSymbol(newTransaction.Instrument, "limit");
				if (session != null)
				{
					var tag50Session = session.Session == MarketSession.Day || session.Session == MarketSession.Globex ? TenantSettingsDictionary.Tag50DaySession : TenantSettingsDictionary.Tag50NightSession;
					var sessionUser = await dbContext.Set<Setting>().AsNoTracking().FirstOrDefaultAsync(x => x.Type == ESettingsType.Tenant && x.Name == tag50Session);

					if (!string.IsNullOrEmpty(sessionUser.Value) && flagIsOn)
					{
						var employee = await dbContext.Employees.FindAsync(Guid.Parse(sessionUser.Value));
						employee.Should().NotBeNull();
						if (!newTransaction.WasAlreadySent)
							newTransaction.UpdatedBy.Should().Be(employee!.Email);
					}
					else
					{
						newTransaction.UpdatedBy.Should().BeNull();
					}
				}
				newTransaction.State.Value.Should().Be(EMarketTransactionState.Pending);
			})
		);
}
