using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Services.ErpIntegration;

namespace RJO.OrderService.WebApi.Tests.DatabaseInitialization;

class AgvantageSettings : TestEntities<AgvantageSetting>
{
	public AgvantageSetting Erp { get; }

	public AgvantageSettings() =>
		Erp = new()
		{
			Name = ErpConstants.Agvantage,
			BaseAddress = "https://beast73.agvantage.com:10090/edgev81/pr/rest",
			PublicKey = "a5aeb072-fb04-4cd6-8525-5e83f971d189",
			Secret = "b3cdf751-47df-449e-a840-56dcb27fa867"
		};

	protected override IReadOnlyList<AgvantageSetting> AllItems =>
	[
		Erp
	];
}
