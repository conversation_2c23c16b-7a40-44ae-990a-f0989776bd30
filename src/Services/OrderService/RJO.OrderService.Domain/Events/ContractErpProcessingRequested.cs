using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.MultiTenancyServer;

namespace RJO.OrderService.Domain.Events;

public sealed class ContractErpProcessingRequested(Guid contractId, string contractEvent, decimal quantityChanged, Contract originalContract, bool updateParent) : IEventPos, IBackgroundTask
{
	public Guid ContractId { get; } = contractId;
	public string ContractEvent { get; } = contractEvent;
	public decimal QuantityChanged { get; } = quantityChanged;
	public Contract OriginalContract { get; } = originalContract;
	public bool UpdateParent { get; } = updateParent;

	public ApplicationTenant ApplicationTenant { get; set; }
}
