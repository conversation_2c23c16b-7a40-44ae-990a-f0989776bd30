using MediatR;
using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Domain.Events;

public sealed class OfferHistoric : IEventPre, INotification
{
	public OfferHistoric(ETransactionEvent eventName, Guid offerId, decimal quantity)
	{
		Event = eventName;
		OfferId = offerId;
		Quantity = quantity;
	}
	
	public Guid OfferId { get; }
	public ETransactionEvent Event { get; }
	public decimal Quantity { get; }
}
