using RJO.BuildingBlocks.Common;
using RJO.OrderService.Domain.Contracts;

namespace RJO.OrderService.Domain.ERP;

public class AgvantageSetting : Entity, IAuditable, ITenantable
{
	public string Name { get; set; }
	public string BaseAddress { get; set; }
	public string PublicKey { get; set; }
	public string Secret { get; set; }

	public AgvantageSetting() => Id = IdentityGenerator.NewSequentialGuid();

	public void Update(bool isActive) => IsActive = isActive;
}
