using RJO.BuildingBlocks.Common;
using RJO.OrderService.Common;
using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Domain.Historical;

public class Bidsheet : Entity, IAuditable, ITenantable
{
	Bidsheet() { }

	public Bidsheet(Guid fileId, Guid commodityId, Guid deliveryLocationId, short cropYear, string delivery, string futureMonth, DateTime deliveryStart, DateTime deliveryEnd, decimal basis, EBidSheetChange change)
	{
		ContractAssertionConcern.CropYearIsValid(cropYear);
		FileId = fileId;
		CommodityId = commodityId;
		CropYear = cropYear;
		DeliveryLocationId = deliveryLocationId;
		Delivery = delivery;
		DeliveryStart = deliveryStart;
		DeliveryEnd = deliveryEnd;
		FutureMonth = futureMonth;
		OldFutureMonth = futureMonth;
		Basis = basis;
		OldBasis = basis;
		Change = change;
		Id = IdentityGenerator.NewSequentialGuid();
		IsActive = true;
	}

	public Guid FileId { get; private set; }
	public Guid CommodityId { get; private set; }
	public Guid DeliveryLocationId { get; private set; }
	public short CropYear { get; private set; }
	public string Delivery { get; private set; }
	public DateTime DeliveryStart { get; private set; }
	public DateTime DeliveryEnd { get; private set; }
	public string FutureMonth { get; private set; }
	public decimal Basis { get; private set; }
	public string OldFutureMonth { get; private set; }
	public decimal OldBasis { get; private set; }
	public EBidSheetChange Change { get; private set; }
}
