using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Domain;

public class OfferInternalState : ValueObject<OfferInternalState>
{
	public EOfferInternalState Value { get; private set; }
	OfferInternalState() { }
	public OfferInternalState(EOfferInternalState status) => Value = status;

	protected override bool EqualsCore(OfferInternalState other) => other != null && other.Value == Value;
	protected override int GetHashCodeCore() => (int)Value;

	public override string ToString() => Enum.GetName(typeof(EOfferInternalState), Value);

	public OfferInternalState Close()
	{
		if (Value == EOfferInternalState.Closed)
		{
			throw new OperationNotPermittedException("This offer is already closed");
		}

		return new(EOfferInternalState.Closed);
	}

	public OfferInternalState ChangeStatusToSentToMarket()
	{
		if (Value == EOfferInternalState.Closed)
		{
			throw new OperationNotPermittedException("Change to sent to market status in this offer is forbidden");
		}

		return new(EOfferInternalState.SentToMarket);
	}

	public OfferInternalState ChangeStatusToLocalMonitored()
	{
		if (Value == EOfferInternalState.Closed)
		{
			throw new OperationNotPermittedException("Change to local monitored status in this offer is forbidden");
		}

		return new(EOfferInternalState.LocalMonitored);
	}

	public bool Is(EOfferInternalState status) => Value == status;

	public OfferInternalState ChangeStatusToBooked()
	{
		if (Value == EOfferInternalState.Booked)
		{
			throw new OperationNotPermittedException("This offer is already booked");
		}

		return new(EOfferInternalState.Booked);
	}

	public OfferInternalState ChangeStatusToCreated()
	{
		if (Value == EOfferInternalState.Created)
		{
			throw new OperationNotPermittedException("This offer is already created");
		}

		return new(EOfferInternalState.Created);
	}
}
