namespace RJO.IntegrationEvents.Commons.Events;

public class CreateOrderLMTEvent : MarketOperationEvent
{
	public decimal LimitPrice { get; set; }
	public string Symbol { get; set; }
	public DateTime? Limit { get; set; }
	public uint Duration { get; set; }
	public bool IsSell { get; set; }
	public string OfferNumber { get; set; }
	public long Quantity { get; set; }
	public string TenantId { get; set; }
	public int AccountId { get; set; }
}
