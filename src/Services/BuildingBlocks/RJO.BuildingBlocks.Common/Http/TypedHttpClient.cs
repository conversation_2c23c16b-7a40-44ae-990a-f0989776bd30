using RJO.BuildingBlocks.Common.Clock;

namespace RJO.BuildingBlocks.Common.Http;

#nullable enable

public abstract class TypedHttpClient : ITypedHttpClient
{
	protected TypedHttpClient(HttpClient httpClient, string? baseAddress, HttpConfiguration configuration, IClock clock)
	{
		HttpClient = httpClient;
		Configuration = configuration;
		if (baseAddress != null)
			HttpClient.BaseAddress = new(baseAddress);
		HttpClient.Timeout = Timeout.InfiniteTimeSpan;
		Clock = clock;
	}

	public HttpClient HttpClient { get; }
	public HttpConfiguration Configuration { get; }
	public IClock Clock { get; }

	public static ArgumentException RequestUriNullException => new($"{nameof(HttpRequestMessage.RequestUri)} may not be null.");

	public async Task<Response> GetHttpResponse(HttpRequestMessage request, CancellationToken cancellationToken)
	{
		var requestStartedAt = Clock.UtcNow;
		var response = await HttpClient.SendAsync(request, cancellationToken);
		var requestEndedAt = Clock.UtcNow;

		var requestDuration = requestEndedAt - requestStartedAt;
		var responseTimestamp = requestStartedAt.Add(requestDuration.Divide(2));

		var headerTimestamp = default(DateTime?);
		if (response.Headers.Date.HasValue)
			headerTimestamp = response.Headers.Date.Value.UtcDateTime;

		return new(requestDuration, responseTimestamp, headerTimestamp, response);
	}

	public abstract Task Authenticate(HttpRequestMessage request, string account, CancellationToken cancellationToken);
}
