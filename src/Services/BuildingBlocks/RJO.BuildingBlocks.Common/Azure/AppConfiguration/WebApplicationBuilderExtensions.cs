using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using RJO.BuildingBlocks.Common;
using System.Reflection;

// ReSharper disable once CheckNamespace
namespace Microsoft.AspNetCore.Builder;

public static class WebApplicationBuilderExtensions
{
	const string ExcludeAzureAppConfigurationKey = "excludeAzureAppConfiguration";

	public static IWebHostBuilder ExcludeAzureAppConfiguration(this IWebHostBuilder builder) => builder.UseSetting(ExcludeAzureAppConfigurationKey, null);

	public static WebApplicationBuilder MaybeAddAzureAppConfiguration(this WebApplicationBuilder builder, StartupLogger logger)
	{
		if (builder.Configuration[ExcludeAzureAppConfigurationKey] != null)
			return builder;

		var endpoint = builder.Configuration["Azure:AppConfiguration:Endpoint"];
		builder.Configuration.AddAzureAppConfiguration(logger, endpoint);

		return builder;
	}
	
	public static WebApplicationBuilder AddLocalUserSecrets(this WebApplicationBuilder builder, Assembly assembly)
	{
		// TODO: User secrets are not loaded automatically
		// when running the project in anything else than Development

#if DEBUG
		builder.Configuration.AddUserSecrets(assembly);
#endif
		
		return builder;
	}
}
