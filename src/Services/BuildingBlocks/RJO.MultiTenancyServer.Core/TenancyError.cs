// Copyright (c) <PERSON>. All rights reserved.
// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.

namespace RJO.MultiTenancyServer.Core;

/// <summary>
/// Encapsulates an error from the tenancy subsystem.
/// </summary>
public class TenancyError
{
	/// <summary>
	/// Gets or sets the code for this error.
	/// </summary>
	/// <value>
	/// The code for this error.
	/// </value>
	public string Code { get; set; }

	/// <summary>
	/// Gets or sets the description for this error.
	/// </summary>
	/// <value>
	/// The description for this error.
	/// </value>
	public string Description { get; set; }
}
