using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;
using RJO.IdentityService.Domain;
using RJO.IdentityService.Domain.Tenants;
using RJO.IdentityService.Persistence.DataBase;
using RJO.IdentityService.Persistence.Repositories;
using RJO.IdentityService.Services.DTO.Role;
using RJO.IdentityService.Services.Helpers;
using RJO.MultiTenancyServer.Core;

namespace RJO.IdentityService.Services.Handlers.Role;

public class UpdateRoleCommand : IRequest<RoleDto>, ITransactionalRequest
{
	public string Name { get; set; }
	public RoleDto Role { get; set; }
}

public class UpdateRoleQueryHandler : IRequestHandler<UpdateRoleCommand, RoleDto>
{
	readonly ApplicationDbContext _unitOfWork;
	readonly ILogger<UpdateRoleQueryHandler> _logger;
	readonly ApplicationRoleRepository _roleRepository;
	readonly ApplicationRoleClaimRepository _roleClaimRepository;
	readonly ITenancyContext<ApplicationTenant> _tenancyContext;

	public UpdateRoleQueryHandler(ApplicationDbContext unitOfWork, ILogger<UpdateRoleQueryHandler> logger,
		ApplicationRoleRepository roleRepository,
		ITenancyContext<ApplicationTenant> tenancyContext,
		ApplicationRoleClaimRepository roleClaimRepository)
	{
		_unitOfWork = unitOfWork;
		_logger = logger;
		_roleRepository = roleRepository;
		_tenancyContext = tenancyContext;
		_roleClaimRepository = roleClaimRepository;
	}

	public async Task<RoleDto> Handle(UpdateRoleCommand request, CancellationToken cancellationToken)
	{
		AssertionConcern.ArgumentIsNotTrue(string.Equals(request.Name, RoleNames.SecurityRole, StringComparison.OrdinalIgnoreCase), "This role is restricted");
		AssertionConcern.ArgumentStringNotNullOrEmpty(request.Role.Name, GeneralResources.RequiredValueIsNotPresent);

		ClaimValidatorHelper.ValidateRoleName(request.Role.Name);
		var role = await (await _roleRepository.GetAllEntities()).Where(x => x.Name == request.Name).FirstOrDefaultAsync(cancellationToken);
		AssertionConcern.ArgumentIsNotNull(role, $"Role with name '{request.Name}' does not exist");
		//AssertionConcern.ArgumentIsNotNull(request.Role.ClaimGrant, GeneralResources.RequiredValueIsNotPresent);
		//AssertionConcern.ArgumentIsBiggerThan(request.Role.ClaimGrant.Count, 0, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotTrue(!await _roleRepository.Exists(request.Name), $"Role with name '{request.Name}' does not exist");

		await RemoveClaims(role.Id);
		if (request.Role.ClaimGrant != null)
		{
			foreach (var claimGrant in request.Role.ClaimGrant)
			{
				if (string.IsNullOrEmpty(claimGrant))
				{
					continue;
				}

				AssertionConcern.ArgumentIsNotTrue(string.Equals(claimGrant, CustomClaimValues.SecurityClaim, StringComparison.OrdinalIgnoreCase), "This claim name is not allowed");
				var roleClaim = new ApplicationRoleClaim(role.Id, CustomClaimTypes.Grant, claimGrant);
				ClaimValidatorHelper.ValidateName(claimGrant);
				await _roleClaimRepository.InsertAsync(roleClaim, cancellationToken);
			}
		}

		if (role.Name != request.Role.Name)
		{
			AssertionConcern.ArgumentIsNotTrue(string.Equals(request.Role.Name, RoleNames.SecurityRole, StringComparison.OrdinalIgnoreCase), "This role is restricted");
			AssertionConcern.ArgumentIsNotTrue(await _roleRepository.Exists(request.Role.Name), "A Role cannot have the same name as another existing role.");
			await _roleRepository.UpdateName(request.Name, request.Role.Name, _tenancyContext.Tenant.Id);
		}

		await _unitOfWork.SaveChangesAsync(cancellationToken);
		return new()
		{
			Name = request.Role.Name,
			ClaimGrant = request.Role.ClaimGrant
		};
	}

	async Task<IList<ApplicationRoleClaim>> RemoveClaims(Guid roleId)
	{
		IList<ApplicationRoleClaim> list = (await _roleClaimRepository.GetAllEntities()).Where(x => x.RoleId == roleId && x.ClaimValue != CustomClaimValues.SecurityClaim).ToList();
		if (list.Any())
		{
			_roleClaimRepository.Delete(list);
		}

		return list;
	}
}
