using Newtonsoft.Json;
using RJO.BuildingBlocks.CustomExceptions.SecurityExceptions;

namespace RJO.IdentityService.WebApi.Identity;

public class OAuth2Client : IDisposable
{
	/// <summary>
	/// The client
	/// </summary>
	protected HttpClient Client;

	bool _disposed;

	/// <summary>
	/// Initializes a new instance of the <see cref="OAuth2Client"/> class.
	/// </summary>
	/// <param name="tenantId">The Tenant Id.</param>
	/// <param name="innerHttpMessageHandler">The inner HTTP message handler.</param>
	/// <exception cref="ArgumentNullException">
	/// address
	/// or
	/// innerHttpMessageHandler
	/// </exception>
	OAuth2Client(Guid tenantId, HttpMessageHandler innerHttpMessageHandler)
	{
		if (innerHttpMessageHandler == null) throw new ArgumentNullException(nameof(innerHttpMessageHandler));

		Client = new(innerHttpMessageHandler);

		Client.DefaultRequestHeaders.Accept.Clear();
		Client.DefaultRequestHeaders.Accept.Add(
			new("application/json"));

		TenantId = tenantId;
	}

	/// <summary>
	/// Initializes a new instance of the <see cref="OAuth2Client"/> class.
	/// </summary>
	/// <param name="tenantId">The Tenant Id.</param>
	/// <param name="clientId">The client identifier.</param>
	/// <param name="resource">The resource.</param>
	[System.Diagnostics.CodeAnalysis.SuppressMessage("Reliability", "CA2000:Dispose objects before losing scope", Justification = "<Pending>")]
	public OAuth2Client(Guid tenantId, string clientId, string resource)
		: this(tenantId, clientId, string.Empty, resource, new HttpClientHandler())
	{
	}

	/// <summary>
	/// Initializes a new instance of the <see cref="OAuth2Client"/> class.
	/// </summary>
	/// <param name="tenantId">The Tenant Id.</param>
	/// <param name="clientId">The client identifier.</param>
	/// <param name="clientSecret">The client secret.</param>
	/// <param name="resource">The resource.</param>
	/// <param name="innerHttpMessageHandler">The inner HTTP message handler.</param>
	/// <exception cref="System.ArgumentNullException">clientId</exception>
	public OAuth2Client(Guid tenantId, string clientId, string clientSecret, string resource, HttpMessageHandler innerHttpMessageHandler)
		: this(tenantId, innerHttpMessageHandler)
	{
		if (string.IsNullOrEmpty(clientId)) throw new ArgumentNullException(nameof(clientId));
		if (string.IsNullOrEmpty(resource)) throw new ArgumentNullException(nameof(resource));

		ClientId = clientId;
		ClientSecret = clientSecret;
		Resource = resource;
	}

	/// <summary>
	/// Gets or sets the client identifier.
	/// </summary>
	/// <value>
	/// The client identifier.
	/// </value>
	string ClientId { get; set; }

	/// <summary>
	/// Gets or sets the client secret.
	/// </summary>
	/// <value>
	/// The client secret.
	/// </value>
	string ClientSecret { get; set; }

	/// <summary>
	/// Gets or sets the resource.
	/// </summary>
	/// <value>
	/// The resource.
	/// </value>
	string Resource { get; set; }

	/// <summary>
	/// Gets or sets the Tenant Id.
	/// </summary>
	/// <value>
	/// The Tenant Id.
	/// </value>
	public Guid TenantId { get; set; }

	/// <summary>
	/// Sets the timeout.
	/// </summary>
	/// <value>
	/// The timeout.
	/// </value>
#pragma warning disable CA1044 // Properties should not be write only
	public TimeSpan Timeout
#pragma warning restore CA1044 // Properties should not be write only
	{
		set => Client.Timeout = value;
	}

	/// <summary>
	/// Sends a token request.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token.</param>
	/// <returns></returns>
#pragma warning disable CS1573 // Parameter has no matching param tag in the XML comment (but other parameters do)
	public virtual async Task<OAuth2AccessToken> RequestAsync(string username, string password, CancellationToken cancellationToken = default)
#pragma warning restore CS1573 // Parameter has no matching param tag in the XML comment (but other parameters do)
	{
		if (string.IsNullOrEmpty(username)) throw new ArgumentNullException(nameof(username));
		if (string.IsNullOrEmpty(password)) throw new ArgumentNullException(nameof(password));


		HttpResponseMessage response;

		var form = new Dictionary<string, string>
		{
			{ "grant_type", "password" },
			{ "client_id", ClientId },
			{ "resource", Resource },
			{ "username", username },
			{ "password", password }
		};

		if (!string.IsNullOrEmpty(ClientSecret))
			form.Add("client_secret", ClientSecret);

		var address = $"https://login.microsoftonline.com/{TenantId}/oauth2/token";

		using var request = new HttpRequestMessage(HttpMethod.Post, address) { Content = new FormUrlEncodedContent(form) };

		request.Headers.Authorization = new BasicAuthenticationHeaderValue(ClientId, ClientSecret);

		var content = string.Empty;

		try
		{
			response = await Client.SendAsync(request, cancellationToken).ConfigureAwait(false);
			content = await response.Content.ReadAsStringAsync(cancellationToken).ConfigureAwait(false);

			response.EnsureSuccessStatusCode();

			return JsonConvert.DeserializeObject<OAuth2AccessToken>(content);
		}
		catch (HttpRequestException ex)
		{
			var error = JsonConvert.DeserializeObject<OAuth2AccessError>(content);

			throw new OAuth2AccessErrorException(error, ex);
		}
	}

	/// <summary>
	/// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
	/// </summary>
	public void Dispose()
	{
		Dispose(true);
		GC.SuppressFinalize(this);
	}

	/// <summary>
	/// Releases unmanaged and - optionally - managed resources.
	/// </summary>
	/// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
	protected virtual void Dispose(bool disposing)
	{
		if (disposing && !_disposed)
		{
			_disposed = true;
			Client.Dispose();
		}
	}
}
